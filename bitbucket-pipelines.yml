image: node:18

pipelines:
  custom:
    RollBack:
      - variables:
        - name: CLIENT_NAME
          default: bryzos
          allowed-values:
            - bryzos
            - csw
            - infra-metals
            - boyd-metals
            - anco-steel
        - name: ENVIRONMENT
          default: staging
          allowed-values:
            - staging
            - qa
            - demo
            - production
        - name: VERSION
      - step:
          name: RollBack $ENVIRONMENT
          script:
            - apt-get update && apt-get install -y awscli jq
            # Read variables from pipeline-config.json
            - S3_BUCKET=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.deployBucket" pipeline-config.json)
            - S3_BUCKET_BACKUP=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.backupBucket" pipeline-config.json)
            - DISTRIBUTION_ID=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.distributionId" pipeline-config.json)
            - aws s3 cp $S3_BUCKET_BACKUP/$VERSION/ $S3_BUCKET --recursive
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
    Deployment:
      - variables:
        - name: CLIENT_NAME
          default: bryzos
          allowed-values:
            - bryzos
            - csw
            - infra-metals
            - boyd-metals
            - anco-steel
        - name: ENVIRONMENT
          default: staging
          allowed-values:
            - staging
            - qa
            - demo
            - production
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"$ENVIRONMENT\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload, Invalidate and Tag
          caches:
            - node
          size: 2x
          script:
            - export NODE_OPTIONS="--max-old-space-size=6144"
            - apt-get update && apt-get install -y awscli jq
            - aws --version # Verify AWS CLI is available
            # Set necessary variables for Current Build   
            - S3_BUCKET=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.deployBucket" pipeline-config.json)
            - S3_BACKUP_BUCKET=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.backupBucket" pipeline-config.json)
            - echo "$S3_BUCKET"
            - echo "$S3_BACKUP_BUCKET"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # start installation
            - npm install
            - export CLIENT_ID=$CLIENT_NAME
            - export NODE_ENV=$ENVIRONMENT
            - npm run web:setup
            # set renderer version
            - TODAY_DATE_V=$(date +'%d-%b-%y')
            - echo "$TODAY_DATE_V"
            - renderer_version="$ENVIRONMENT-$TODAY_DATE_V-$build_number"
            - echo "$renderer_version"
            - env_fileName=".env.$ENVIRONMENT"
            - node ./scripts/update-renderer "$renderer_version" "$env_fileName"
            # start build
            - npm run build:web:$ENVIRONMENT
            # Set necessary variables for Backup        
            - LOCAL_PATH="./build/"
            - EXCLUDE_FILES='--exclude=robots.txt'
            # Deploy backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/ $EXCLUDE_FILES
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ $EXCLUDE_FILES --recursive
            - aws s3 sync $LOCAL_PATH $S3_BUCKET/ --delete
            - aws s3 cp buildversion1.ver $S3_BUCKET
            # Trigger CloudFront invalidation and capture the response
            - DISTRIBUTION_ID=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.distributionId" pipeline-config.json)
            - echo "$DISTRIBUTION_ID"
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
            # Notify external service about new version
            - REFRESH_URL=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.refreshUrl" pipeline-config.json)
            - echo "$REFRESH_URL"
            - >
              curl --location "https://${REFRESH_URL}/external-affairs/render/version" --header 'Content-Type: application/json' --data "{\"data\": \"$renderer_version\"}"
            # Create tag
            - TAG_EXISTS=$(git tag -l "$CLIENT_NAME-$ENVIRONMENT-$(date +'%d-%b-%y')-*" | wc -l)
            - echo "$TAG_EXISTS";
            - if [ "$TAG_EXISTS" -eq 0 ]; then
               NEW_TAG="$CLIENT_NAME-$ENVIRONMENT-$(date +'%d-%b-%y')-1";
              else
               LAST_TAG=$(git tag -l "$CLIENT_NAME-$ENVIRONMENT-$(date +'%d-%b-%y')-*" | sort -r | head -n 1);
               LAST_NUMBER=$(echo "$LAST_TAG" | sed 's/.*-\([0-9]*\)$/\1/');
               NEW_NUMBER=$((LAST_NUMBER + 1));
               NEW_TAG="$CLIENT_NAME-$ENVIRONMENT-$(date +'%d-%b-%y')-$NEW_NUMBER";
              fi
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "tag created successfully"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi  
    Testing:
      - variables:
        - name: CLIENT_NAME
          default: bryzos
          allowed-values:
            - bryzos
            - csw
            - infra-metals
            - boyd-metals
            - anco-steel
        - name: ENVIRONMENT
          default: staging
          allowed-values:
            - staging
            - qa
            - demo
            - production
      - step:
          name: $CLIENT_NAME $ENVIRONMENT Build, Upload, Invalidate and Tag
          caches:
            - node
          size: 2x
          script:
            # Read variables from pipeline-config.json
            - apt-get update && apt-get install -y awscli jq
            - npm install
            - export CLIENT_ID=$CLIENT_NAME
            - export NODE_ENV=$ENVIRONMENT
            - npm run web:setup
            - VITE_API_SERVICE=$(grep "^VITE_API_SERVICE=" .env.${ENVIRONMENT} | cut -d '=' -f2 | tr -d '"' | tr -d "'")
            - echo "VITE_API_SERVICE=$VITE_API_SERVICE"
            - S3_BUCKET=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.deployBucket" pipeline-config.json)
            - S3_BACKUP_BUCKET=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.backupBucket" pipeline-config.json)
            - DISTRIBUTION_ID=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.distributionId" pipeline-config.json)
            - REFRESH_URL=$(jq -r ".[\"${CLIENT_NAME}\"].${ENVIRONMENT}.refreshUrl" pipeline-config.json)
            - echo "$S3_BUCKET"
            - echo "$S3_BACKUP_BUCKET"
            - echo "$DISTRIBUTION_ID"
            - echo "$REFRESH_URL"    
