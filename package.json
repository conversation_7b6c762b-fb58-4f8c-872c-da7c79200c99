{"name": "bryzos-extended-pricing-widget", "productName": "<PERSON><PERSON><PERSON><PERSON> Gone In 60 Seconds (GISS) 2.0", "version": "2.1.2", "private": true, "main": "./.webpack/main", "scripts": {"dev": "concurrently \"npm run start:web\" \"npm run start:electron\"", "start:electron": "cross-env CLIENT_ID=bryzos NODE_ENV=development npm run electron:setup && electron-forge start", "package": "electron-forge package", "make": "electron-forge make --arch=arm64,x64", "make-windows": "electron-forge make", "build:electron:staging": "cross-env NODE_ENV=staging CLIENT_ID=bryzos npm run electron:setup && cross-env NODE_ENV=staging CLIENT_ID=bryzos npm run make-windows", "build:electron:staging:mac": "cross-env NODE_ENV=staging CLIENT_ID=bryzos npm run electron:setup && cross-env NODE_ENV=staging CLIENT_ID=bryzos npm run make", "build:electron:qa": "cross-env NODE_ENV=qa CLIENT_ID=bryzos npm run electron:setup && cross-env NODE_ENV=qa CLIENT_ID=bryzos npm run make-windows", "build:electron:qa:mac": "cross-env NODE_ENV=qa CLIENT_ID=bryzos npm run electron:setup && cross-env NODE_ENV=qa CLIENT_ID=bryzos npm run make", "build:electron:demo": "cross-env NODE_ENV=demo CLIENT_ID=bryzos npm run electron:setup && cross-env NODE_ENV=demo CLIENT_ID=bryzos npm run make-windows", "build:electron:demo:mac": "cross-env NODE_ENV=demo CLIENT_ID=bryzos npm run electron:setup && cross-env NODE_ENV=demo CLIENT_ID=bryzos npm run make", "build:electron:production": "cross-env NODE_ENV=production CLIENT_ID=bryzos npm run electron:setup && cross-env NODE_ENV=production CLIENT_ID=bryzos npm run make-windows", "build:electron:production:mac": "cross-env NODE_ENV=production CLIENT_ID=bryzos npm run electron:setup && cross-env NODE_ENV=production CLIENT_ID=bryzos npm run make", "test": "vite test", "start:web": "cross-env CLIENT_ID=bryzos NODE_ENV=development npm run web:setup && vite --force --mode development", "build:web": "vite build --mode development", "build:web:staging": "vite build --mode staging", "build:web:qa": "vite build --mode qa", "build:web:demo": "vite build --mode demo", "build:web:production": "vite build --mode production", "config-generate-electron": "node ./scripts/configMoveElectron.mjs", "electron:setup": "node ./scripts/buildElectron.mjs", "web:setup": "node ./scripts/buildWeb.mjs", "client:restore": "node -e \"require('./scripts/clientConfig.mjs').restorePackageJson()\""}, "dependencies": {"@bryzos/giss-common-lib": "1.2.0", "@bryzos/giss-ui-library": "1.0.175", "@bryzos/steel-search-lib": "1.0.5", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@hookform/resolvers": "^3.0.0", "@mantine/hooks": "^6.0.1", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.11.10", "@mui/x-date-pickers": "^6.1.0", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.1.0", "@tanstack/react-query": "^4.27.0", "@tanstack/react-query-devtools": "^4.27.0", "ag-grid-community": "^34.0.2", "ag-grid-react": "^34.0.2", "aws-amplify": "^5.0.20", "axios": "^1.3.4", "compare-versions": "^6.1.0", "dayjs": "^1.11.7", "electron-localshortcut": "^3.2.1", "electron-log": "^5.2.4", "electron-squirrel-startup": "^1.0.0", "electron-store": "^8.1.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "immer": "^10.0.1", "leo-profanity": "^1.8.0", "moment": "^2.29.4", "moment-timezone": "^0.5.42", "node-abi": "3.24.0", "node-machine-id": "1.1.12", "pdfjs-dist": "^5.2.133", "pdfmake": "^0.2.8", "planck-js": "^1.3.0", "postcss": "^8.4.21", "postcss-pxtorem": "^6.0.0", "pusher-js": "^8.2.0", "raygun4js": "^2.25.6", "rbush": "^4.0.1", "react": "^18.2.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.8", "react-hook-form": "^7.43.6", "react-input-emoji": "^5.9.0", "react-odometerjs": "^3.1.3", "react-router-dom": "^6.8.2", "react-share": "^5.1.0", "socket.io-client": "^4.6.1", "truevault": "^1.3.1", "use-immer": "^0.9.0", "uuid": "^9.0.0", "web-vitals": "^3.3.0", "xlsx": "^0.18.5", "yup": "^1.0.2", "zustand": "^4.3.6"}, "devDependencies": {"@electron-forge/cli": "^6.0.5", "@electron-forge/maker-deb": "^6.0.5", "@electron-forge/maker-dmg": "^6.2.1", "@electron-forge/maker-rpm": "^6.0.5", "@electron-forge/maker-squirrel": "^6.0.5", "@electron-forge/maker-zip": "^6.1.1", "@electron-forge/plugin-webpack": "^6.0.5", "@plugin-web-update-notification/vite": "^1.6.1", "@testing-library/jest-dom": "^6.1.2", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/file-saver": "^2.0.5", "@types/node": "^20.4.8", "@types/raygun4js": "^2.13.8", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "@types/uuid": "^9.0.2", "@vercel/webpack-asset-relocator-loader": "1.7.3", "@vitejs/plugin-react": "^4.0.4", "clsx": "^2.0.0", "concurrently": "^8.0.1", "copy-webpack-plugin": "^12", "cross-env": "^7.0.3", "electron": "^34.5.7", "electron-rebuild": "^3.2.9", "node-loader": "^2.0.0", "postcss": "^8.4.21", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.64.2", "ts-loader": "^9.4.4", "ts-node": "^10.9.1", "typescript": "^5", "vite": "^4.2.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-svgr": "^3.2.0", "vite-tsconfig-paths": "^4.0.7"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}