import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import styles from './ListTab.module.scss';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { MenuItem, Select } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { bryzosClientId, localStorageKeys, routes } from 'src/renderer/common';
import SavedPricingTemplate from '../Templates/SavedPricingTemplate';
import clsx from 'clsx';
import { useLeftPanelStore } from '../LeftPanelStore';
import { noIdGeneric, useBuyerSettingStore, useCreatePoStore, useGlobalStore, useOrderManagementStore, userRole, useSearchStore } from '@bryzos/giss-ui-library';
import { calculateTotalPurchase, clearLocal, fetchPrice, getLocal, navigatePage, newPriceFormatter, updateOrderManagementData } from 'src/renderer/helper';
import usePostSaveSearchProducts from 'src/renderer/hooks/usePostSaveSearchProducts';
import { ReactComponent as CreateNewButton } from '../../../assets/images/create-new-button.svg';
import { ReactComponent as CreateNewButtonHover } from '../../../assets/images/create-new-button-hover.svg';
import { ReactComponent as DeleteIcon } from '../../../assets/images/delete-outlined.svg';
import { ReactComponent as DropdownArrow } from '../../../assets/images/Polygon.svg';
import { ReactComponent as CreateNewPluseIcon } from '../../../assets/images/createNewPluse.svg';
import { ReactComponent as CreateNewPluseIconHover } from '../../../assets/images/createNewPluseHover.svg';
import { ReactComponent as LeftSideArrow } from '../../../assets/images/back-button-icon-container.svg';
import useDeleteSearchProducts from 'src/renderer/hooks/useDeleteSearchProducts';
import DraftPoTemplate from '../Templates/DraftPoTemplate';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import {  useGlobalSearchStore } from 'src/renderer/pages/GlobalSearchField/globalSearchStore';
import {useDeletedItemsStore} from '../../../store/DeletedItemsStore'
import { useBomPdfExtractorStore } from 'src/renderer/pages/buyer/BomPdfExtractor/BomPdfExtractorStore';
import usePostCancelDraftPo from 'src/renderer/hooks/usePostCancelDraftPo';
import OrderManagementTemplate from '../Templates/OrderManagementTemplate';
import useGetOrderLines from 'src/renderer/hooks/useGetOrderLines';

dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(customParseFormat);

// Define types for better TypeScript support

type SourceTag = 'pricing' | 'quote' | 'purchase' | 'order';

type SearchModeItem = SearchProduct & { __source: SourceTag };

type IdObj = { id: string } & Record<string, any>;

interface GlobalSearchResult {
  pricing?: IdObj[];
  quote?: IdObj[];
  purchase?: IdObj[];
  order?: IdObj[]; // keep this too (same shape)
}

interface SearchProduct {
  id: string;
  title: string;
  buyer_internal_po: string;
  buyer_po_price: string;
  created_date: string;
  time_stamp?: string;
  search_date_time: string;
  item_count: number;
  amount?: number;
  name?: string;
  products: Array<{ shape?: string }>;
  order_size: number;
  claimed_date?: string;
}

interface GroupedData {
  [key: string]: SearchProduct[];
}

// Unified grouping function that works for all filters
export const groupByTimeLabels = (items: SearchProduct[], sortOrder: 'newest' | 'oldest' = 'newest', field: string = 'created_date', userType: string, route: string): GroupedData => {
  const now = dayjs();
  const groups: GroupedData = {
    'Today': [],
    'Yesterday': [],
    'This Week': [],
    'Last Week': [],
    'This Month': [],
    'Last Month': [],
    'This Year': [],
    'Older': []
  };


  // If no items, return Today group with empty list
  if (!items || items.length === 0) {
    return {
      'Today': []
    };
  }

  items?.forEach((item: SearchProduct) => {
    // Use the specified field for grouping, fallback to created_date if time_stamp is not available
    const dateField = (userType === userRole.sellerUser && route === routes.orderManagementPage) ? item?.claimed_date : item?.created_date;
    const itemDate = dayjs.utc(dateField);
    
    if (itemDate.isToday()) {
      groups['Today'].push(item);
    } else if (itemDate.isYesterday()) {
      groups['Yesterday'].push(item);
    } else if (itemDate.isSame(now, 'week')) {
      groups['This Week'].push(item);
    } else if (itemDate.isSame(now.subtract(1, 'week'), 'week')) {
      groups['Last Week'].push(item);
    } else if (itemDate.isSame(now, 'month')) {
      groups['This Month'].push(item);
    } else if (itemDate.isSame(now.subtract(1, 'month'), 'month')) {
      groups['Last Month'].push(item);
    } else if (itemDate.isSame(now, 'year')) {
      groups['This Year'].push(item);
    } else {
      groups['Older'].push(item);
    }
  });

  // Remove empty groups
  Object.keys(groups).forEach(key => {
    if (groups[key].length === 0) {
      delete groups[key];
    }
  });

  // If no groups remain after filtering, return Today group with empty list
  if (Object.keys(groups).length === 0) {
    return {
      'Today': []
    };
  }

  // Sort groups based on sortOrder
  const groupOrder = {
    'newest': ['Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month', 'This Year', 'Older'],
    'oldest': ['Older', 'This Year', 'Last Month', 'This Month', 'Last Week', 'This Week', 'Yesterday', 'Today']
  };

  const sortedGroups: GroupedData = {};
  const order = groupOrder[sortOrder];
  
  order.forEach(groupName => {
    if (groups[groupName]) {
      sortedGroups[groupName] = groups[groupName];
    }
  });

  return sortedGroups;
};

  const getSortField = (route: string, filterType: string): string => {
  if (filterType === 'newest' || filterType === 'oldest') {
    switch (route) {
      case routes.homePage: return 'created_date'; // Instant Price Search - Date list was created
      case routes.quotePage: return 'created_date'; // Quoting - Date quote was most recently modified
      case routes.createPoPage: return 'created_date'; // Purchasing - Date order was most recently modified
      case routes.orderManagementPage: return 'created_date'; // Order Management - Purchase date
      default: return 'created_date';
    }
  }
  
  if (filterType === 'a-to-z' || filterType === 'z-to-a') {
    switch (route) {
        case routes.homePage: return 'title';
        case routes.quotePage: return 'buyer_internal_po';
        case routes.createPoPage: return 'buyer_internal_po';
        case routes.orderManagementPage: return 'buyer_internal_po';
        default: return 'buyer_internal_po';
    }
  }
  
  if (filterType === 'highest' || filterType === 'lowest') {
    switch (route) {
      case routes.homePage: return 'item_count'; // Instant Price Search - Number of items
      case routes.quotePage: return 'buyer_po_price'; // Quoting - Total dollar value
      case routes.createPoPage: return 'buyer_po_price'; // Purchasing - Total dollar value
      case routes.orderManagementPage: return 'total_purchase'; // Order Management - Total dollar value
      default: return 'item_count';
    }
  }
  
  return 'created_date';
};

// Filter functions for each menu item - all using the same grouping system
const filterByNewest = (data: SearchProduct[], route: string, userType: string): GroupedData => {
 const field = getSortField(route, 'newest');
 const grouped = groupByTimeLabels(data, 'newest', field, userType, route);
  
  // Sort each group by the specified field (newest first)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      const aCreatedDate = dayjs.utc((userType === userRole.sellerUser && route === routes.orderManagementPage) ? a.claimed_date : a.created_date);
      const bCreatedDate = dayjs.utc((userType === userRole.sellerUser && route === routes.orderManagementPage) ? b.claimed_date : b.created_date);
      switch (field) {
        case 'created_date':
          return bCreatedDate.diff(aCreatedDate);
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'item_count':
          return (b.item_count || 0) - (a.item_count || 0);
        case 'order_size':
          return (b.order_size || 0) - (a.order_size || 0);
        default:
          return bCreatedDate.diff(aCreatedDate);
      }
    });
  });
  
  return sortedGrouped;
};

const filterByOldest = (data: SearchProduct[], route: string, userType: string): GroupedData => {
  const field = getSortField(route, 'oldest');
  const grouped = groupByTimeLabels(data, 'oldest', field, userType, route);
  
  // Sort each group by the specified field (oldest first)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      const aCreatedDate = dayjs.utc((userType === userRole.sellerUser && route === routes.orderManagementPage) ? a.claimed_date : a.created_date);
      const bCreatedDate = dayjs.utc((userType === userRole.sellerUser && route === routes.orderManagementPage) ? b.claimed_date : b.created_date);
      const aTimeStamp = dayjs.utc(a.time_stamp || a.created_date);
      const bTimeStamp = dayjs.utc(b.time_stamp || b.created_date);
      switch (field) {
        case 'created_date':
          return aCreatedDate.diff(bCreatedDate);
        case 'time_stamp':
          return aTimeStamp.diff(bTimeStamp);
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'item_count':
          return (a.item_count || 0) - (b.item_count || 0);
        case 'order_size':
          return (a.order_size || 0) - (b.order_size || 0);
        default:
          return aCreatedDate.diff(bCreatedDate);
      }
    });
  });
  
  return sortedGrouped;
};

const filterByAToZ = (data: SearchProduct[], route: string, userType: string): GroupedData => {
    const field = getSortField(route, 'a-to-z');
  const grouped = groupByTimeLabels(data, 'newest', field, userType, route);
  
  // Sort each group alphabetically by the specified field (A to Z)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      
      switch (field) {
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'buyer_internal_po':
          return (a.buyer_internal_po || '').toLowerCase().localeCompare((b.buyer_internal_po || '').toLowerCase());
        default:
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
      }
    });
  });
  
  return sortedGrouped;
};

const filterByZToA = (data: SearchProduct[], route: string, userType: string): GroupedData => {
  const field = getSortField(route, 'z-to-a');
  const grouped = groupByTimeLabels(data, 'newest', field, userType, route);
  
  // Sort each group alphabetically by the specified field (Z to A)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      switch (field) {
        case 'title':
          return (b.title || '').toLowerCase().localeCompare((a.title || '').toLowerCase());
        case 'buyer_internal_po':
          return (b.buyer_internal_po || '').toLowerCase().localeCompare((a.buyer_internal_po || '').toLowerCase());
        default:
          return (b.title || '').toLowerCase().localeCompare((a.title || '').toLowerCase());
      }
    });
  });
  
  return sortedGrouped;
};

const filterByHighest = (data: SearchProduct[], route: string, userType: string): GroupedData => {
    const field = getSortField(route, 'highest');
  const grouped = groupByTimeLabels(data, 'newest', field, userType, route);
  
  // Sort each group by the specified field (highest to lowest)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      
      switch (field) {
        case 'item_count':
          return (b.item_count || 0) - (a.item_count || 0);
        case 'buyer_po_price':
            return calculateTotalPurchase(b) - calculateTotalPurchase(a);
        case 'total_purchase':
            return calculateTotalPurchase(b) - calculateTotalPurchase(a);
        case 'order_size':
          return (b.order_size || 0) - (a.order_size || 0);
        default:
          return (b.item_count || 0) - (a.item_count || 0);
      }
    });
  });
  
  return sortedGrouped;
};

const filterByLowest = (data: SearchProduct[], route: string, userType: string): GroupedData => {
  const field = getSortField(route, 'lowest');
  const grouped = groupByTimeLabels(data, 'newest', field, userType, route);
  
  // Sort each group by the specified field (lowest to highest)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {

        
       
      switch (field) {
        case 'item_count':
          return (a.item_count || 0) - (b.item_count || 0);
        case 'buyer_po_price':
            return calculateTotalPurchase(a) - calculateTotalPurchase(b);
        case 'total_purchase':
            return calculateTotalPurchase(a) - calculateTotalPurchase(b);
        case 'order_size':
          return (a.order_size || 0) - (b.order_size || 0);
        default:
            return (Number(a.buyer_po_price) || 0) - (Number(b.buyer_po_price) || 0);
      }
    });
  });
  
  return sortedGrouped;
};


const getAlphaKey = (it: Partial<SearchModeItem>) =>
  ((it.title as string) ?? (it as any).buyer_internal_po ?? (it as any).name ?? '').toLowerCase();

const getNumericKey = (it: SearchModeItem) => {
  switch (it.__source) {
    case 'pricing':
      return it.item_count || 0;
    case 'quote':
    case 'purchase':
      return calculateTotalPurchase(it as any) || 0;
    case 'order':
      return (it as any).total_amount || calculateTotalPurchase(it as any) || 0;
    default:
      return 0;
  }
};

const getDateKey = (it: any) => dayjs.utc(it?.time_stamp || it?.created_date);

// ---- NEW: Group & sort for search mode (works with your existing labels) ----
const filterSearchMode = (data: SearchModeItem[], viewFilter: string): GroupedData => {
  const primaryOrder: 'newest' | 'oldest' = viewFilter === 'oldest' ? 'oldest' : 'newest';
  const grouped = groupByTimeLabels(data, primaryOrder, 'time_stamp'); // falls back to created_date

  const sortedGrouped: GroupedData = {};
  Object.keys(grouped).forEach(label => {
    const arr = grouped[label] as SearchModeItem[];
    let sorted = [...arr];

    if (viewFilter === 'newest') {
      sorted.sort((a, b) => getDateKey(b).diff(getDateKey(a)));
    } else if (viewFilter === 'oldest') {
      sorted.sort((a, b) => getDateKey(a).diff(getDateKey(b)));
    } else if (viewFilter === 'a-to-z') {
      sorted.sort((a, b) => getAlphaKey(a).localeCompare(getAlphaKey(b)));
    } else if (viewFilter === 'z-to-a') {
      sorted.sort((a, b) => getAlphaKey(b).localeCompare(getAlphaKey(a)));
    } else if (viewFilter === 'highest') {
      sorted.sort((a, b) => getNumericKey(b) - getNumericKey(a));
    } else if (viewFilter === 'lowest') {
      sorted.sort((a, b) => getNumericKey(a) - getNumericKey(b));
    }

    sortedGrouped[label] = sorted;
  });

  return sortedGrouped;
};


const animatedItems: Set<string> = new Set();

const ListTab = () => {
  const location = useLocation();
  const setSelectedSavedSearch = useSearchStore(state => state.setSelectedSavedSearch );
  const setShortListedSearchProductsData = useSearchStore(state => state.setShortListedSearchProductsData);
  const setFilterShortListedSearchProductsData = useSearchStore(state => state.setFilterShortListedSearchProductsData);
  const setSelectedProductsData = useSearchStore(state => state.setSelectedProductsData);
  const setSaveFeedbackMap = useSearchStore(state => state.setSaveFeedbackMap);
  const setFocusSingleProduct = useSearchStore(state => state.setFocusSingleProduct);
  const savedSearchProducts = useSearchStore(state => state.savedSearchProducts);
  const setSavedSearchProducts = useSearchStore(state => state.setSavedSearchProducts);
  const quoteList = useCreatePoStore(state => state.quoteList);
  const setQuoteList = useCreatePoStore(state => state.setQuoteList);
  const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
  const selectedQuote = useCreatePoStore(state => state.selectedQuote);
  const purchasingList = useCreatePoStore(state => state.purchasingList);
  const setPurchasingList = useCreatePoStore(state => state.setPurchasingList);
  const shortListedSearchProductsData = useSearchStore(state => state.shortListedSearchProductsData);
  const selectedSavedSearch = useSearchStore(state => state.selectedSavedSearch);
  const clickedCreateNewButton = useLeftPanelStore(state => state.clickedCreateNewButton);
  const setClickedCreateNewButton = useLeftPanelStore(state => state.setClickedCreateNewButton);
  const setClickThisId = useLeftPanelStore(state => state.setClickThisId);
  const clickThisId = useLeftPanelStore(state => state.clickThisId);
  const {deletedItems: buyerDeletedItemsData} = useDeletedItemsStore();
  const [grouped, setGrouped] = useState<GroupedData>({});
  const [viewFilter, setViewFilter] = useState<string>('newest');
  const [selectOpen, setSelectOpen] = useState<boolean>(false);
  const [deleteSelectOpen, setDeleteSelectOpen] = useState<boolean>(false);
  const [deleteViewFilter, setDeleteViewFilter] = useState<'instant_price_search' | 'quoting' | 'purchasing'>(import.meta.env.VITE_CLIENT_ID === bryzosClientId ? 'instant_price_search' : 'quoting');
  const isSearchMode = useGlobalSearchStore(state => state.isSearchMode);
  const searchResult = useGlobalSearchStore(state => state.searchResult);
  const setIsSearchMode = useGlobalSearchStore(state => state.setIsSearchMode);
  const openOrderFromList = useOrderManagementStore(state => state.openOrderFromList);
  const setOpenOrderFromList = useOrderManagementStore(state => state.setOpenOrderFromList);
  const removedSavedSearchDataFromSocket = useSearchStore(state => state.removedSavedSearchDataFromSocket);
  const setRemovedSavedSearchDataFromSocket = useSearchStore(state => state.setRemovedSavedSearchDataFromSocket);
  const draftDeletedSocketData = useCreatePoStore((state: any) => state.draftDeletedSocketData);
  const setDraftDeletedSocketData = useCreatePoStore((state: any) => state.setDraftDeletedSocketData);
const updatedSavedSearchDataFromSocket = useSearchStore((state: any) => state.updatedSavedSearchDataFromSocket);
const setUpdatedSavedSearchDataFromSocket = useSearchStore((state: any) => state.setUpdatedSavedSearchDataFromSocket);
const draftOrderListFromSocket = useCreatePoStore((state: any) => state.draftOrderListFromSocket);
const setDraftOrderListFromSocket = useCreatePoStore((state: any) => state.setDraftOrderListFromSocket);
const isDisableLeftPanel = useLeftPanelStore(state => state.isDisableLeftPanel);
const listTabScrollRef = useRef<HTMLDivElement>(null);

const getDeleteData = useCallback(
  (key: "instant_price_search" | "quoting" | "purchasing") => {
    let data: any[] = [];
    if (key === "instant_price_search") {
      data = buyerDeletedItemsData.INSTANT_PRICING;
    } else if (key === "quoting") {
      data = buyerDeletedItemsData.QUOTE;
    } else if (key === "purchasing") {
      data = buyerDeletedItemsData.PO;
    }

    return data;
  },
  [buyerDeletedItemsData]
);
  
  // Single dropdown state
  const [lastClickedIndex, setLastClickedIndex] = useState<number | null>(null);
  const [selectedSavedSearchIdList, setSelectedSavedSearchIdList] = useState<any[]>([]);

  // Undo delete state
  const [showUndoDelete, setShowUndoDelete] = useState<boolean>(false);
  const [deletedItems, setDeletedItems] = useState<any[]>([]);
  const [undoTimer, setUndoTimer] = useState<NodeJS.Timeout | null>(null);
  const [undoCountdown, setUndoCountdown] = useState<number>(10);

  const { mutateAsync: saveSearchProductsMutation } = usePostSaveSearchProducts();
  const { mutateAsync: deleteSearchProductsMutation } = useDeleteSearchProducts();
  const {mutateAsync: cancelDraftPo} = usePostCancelDraftPo();
  const {setBomUploadID, bomUploadID, setPdfFile, setAllBoxes, setPdfUrl, setPdfFileName} = useBomPdfExtractorStore();
  const {setUploadBomInitialData} = useCreatePoStore();
  const orderManagementData = useOrderManagementStore(state => state.orderManagementData);
  const isHomePage = location.pathname === routes.homePage;
  const isQuotePage = location.pathname === routes.quotePage;
  const isPurchasingPage = location.pathname === routes.createPoPage || location.pathname === routes.orderConfirmationPage;
  const isOrderManagementPage = location.pathname === routes.orderManagementPage;
  const isDeletePage = location.pathname === routes.buyerDeleteOrderPage
  const { setSelectedIndex, setSelectedObject , selectedObject} = useGlobalSearchStore();
  const {navigationStateForNotification , setNavigationStateForNotification , setShowLoader} = useGlobalStore();
  const {mutateAsync: getOrderLines} = useGetOrderLines();
  const {userData} = useGlobalStore();
  const isSeller = userData?.data?.type === userRole.sellerUser;
  const addToDeletedItems = useDeletedItemsStore(state => state.addToDeletedItems);

  const itemRefs = useRef<Record<string, HTMLElement | null>>({});
  const quoteItemRefs = useRef<Record<string, HTMLElement | null>>({});
  const orderItemRefs = useRef<Record<string, HTMLElement | null>>({});

  const scrollToItem = (itemRef: HTMLElement | null) => {
    if (!itemRef) return;
    
    const container = document.querySelector(`.${styles.savedSearchListContainer}`);
    if (!container) return;
    
    const containerRect = container.getBoundingClientRect();
    const itemRect = itemRef.getBoundingClientRect();
    
    // Calculate if item is outside visible area
    const isAboveView = itemRect.top < containerRect.top;
    const isBelowView = itemRect.bottom > containerRect.bottom;
    
    if (isAboveView || isBelowView) {
      const scrollOffset = itemRect.top - containerRect.top - 20; // 20px padding from top
      container.scrollBy({ top: scrollOffset, behavior: 'smooth' });
    }
  };

  useEffect(() => {
    if (listTabScrollRef.current) {
      listTabScrollRef.current.scrollTop = 0;
    }
  }, [location.pathname]);

  useEffect(()=>{
    if(selectedSavedSearch){
      setSelectedQuote(null);
    }
  },[selectedSavedSearch]);

  useEffect(() => {

    if (removedSavedSearchDataFromSocket?.removed_ids && removedSavedSearchDataFromSocket.removed_ids.length > 0) {
      const currentSavedSearchProducts = savedSearchProducts;
      
      const itemsToMoveToDeleted = currentSavedSearchProducts.filter(item => 
        removedSavedSearchDataFromSocket.removed_ids.includes(item.id)
      );
      
      if (itemsToMoveToDeleted.length > 0) {
        const itemsWithOrderType = itemsToMoveToDeleted.map(item => ({
          ...item,
          order_type: 'INSTANT_PRICING',
          is_deleted: 1,
          pricing_expired: 1
        }));

         addToDeletedItems(itemsWithOrderType, 'INSTANT_PRICING');
      }
      
      const updatedSavedSearchProducts = currentSavedSearchProducts.filter(item => 
        !removedSavedSearchDataFromSocket.removed_ids.includes(item.id)
      );
      
      if (updatedSavedSearchProducts.length !== currentSavedSearchProducts.length) {
        setSavedSearchProducts(updatedSavedSearchProducts);
      }
      setRemovedSavedSearchDataFromSocket(null);
    }
  }, [removedSavedSearchDataFromSocket, savedSearchProducts, setSavedSearchProducts, addToDeletedItems]);

  useEffect(() => {
    if (draftDeletedSocketData?.id && draftDeletedSocketData.id.length > 0) {
      const { order_type } = draftDeletedSocketData;
      const deletedIds = draftDeletedSocketData.id;
      
      if (order_type === 'QUOTE') {
        const itemsToMoveToDeleted = quoteList.filter(item => 
          deletedIds.includes(item.id)
        );
        
        if (itemsToMoveToDeleted.length > 0) {
          addToDeletedItems(itemsToMoveToDeleted, 'QUOTE');
        }
        
        const updatedQuoteList = quoteList.filter(item => 
          !deletedIds.includes(item.id)
        );
        setQuoteList(updatedQuoteList);
      } else if (order_type === 'PO') {
        const itemsToMoveToDeleted = purchasingList.filter(item => 
          deletedIds.includes(item.id)
        );
        
        if (itemsToMoveToDeleted.length > 0) {
          addToDeletedItems(itemsToMoveToDeleted, 'PO');
        }
        
        const updatedPurchasingList = purchasingList.filter(item => 
          !deletedIds.includes(item.id)
        );
        setPurchasingList(updatedPurchasingList);
      }
      
      setDraftDeletedSocketData(null);
    }
  }, [draftDeletedSocketData, quoteList, purchasingList, setQuoteList, setPurchasingList, setDraftDeletedSocketData, addToDeletedItems]);

  useEffect(() => {
    if (draftOrderListFromSocket && draftOrderListFromSocket.length > 0) {
      const quoteItems = draftOrderListFromSocket
        .filter(item => item.order_type === 'QUOTE')
        .map(item => item.id);
      
      const poItems = draftOrderListFromSocket
        .filter(item => item.order_type === 'PO')
        .map(item => item.id);
      
      const { removeFromDeletedItems } = useDeletedItemsStore.getState();
      
      if (quoteItems.length > 0) {
        removeFromDeletedItems(quoteItems, 'QUOTE');
      }
      if (poItems.length > 0) {
        removeFromDeletedItems(poItems, 'PO');
      }
      
      setDraftOrderListFromSocket([]);
    }
  }, [draftOrderListFromSocket, setDraftOrderListFromSocket]);

  useEffect(() => {
    if (updatedSavedSearchDataFromSocket?.data?.id) {
      const itemId = updatedSavedSearchDataFromSocket.data.id;
      
      const { removeFromDeletedItems } = useDeletedItemsStore.getState();
      removeFromDeletedItems([itemId], 'INSTANT_PRICING');
      
      setUpdatedSavedSearchDataFromSocket(null);
    }
  }, [updatedSavedSearchDataFromSocket, setUpdatedSavedSearchDataFromSocket]);

  useEffect(()=>{
    if(selectedQuote){
      setSelectedSavedSearch(null);
    }
  },[selectedQuote]); 

  useEffect(() => {
    if(location.pathname){
      setSelectedSavedSearchIdList([]);
    }
  }, [location.pathname])


  useEffect(() => {
    let poNumber = navigationStateForNotification?.referenceId || openOrderFromList;
    if(poNumber && isOrderManagementPage && orderManagementData && orderManagementData.length > 0){
      const sortedOrders = [...orderManagementData].sort((a, b) => {
        const aCreatedDate = userData?.data?.type === userRole.buyerUser ? dayjs.utc(a.created_date) : dayjs.utc(a.claimed_date);
        const bCreatedDate = userData?.data?.type === userRole.buyerUser ? dayjs.utc(b.created_date) : dayjs.utc(b.claimed_date);
        return bCreatedDate.diff(aCreatedDate);
      });
      const firstOrder = sortedOrders[0];
      const foundItem = orderManagementData.find((item: any) => {
        if(userData?.data?.type === userRole.sellerUser && item.seller_po_number === poNumber.replace('S', 'P')){ 
          return true;
        }else if(userData?.data?.type === userRole.buyerUser && item.buyer_po_number === poNumber){ 
          return true;
        }
        return false;
      });
      setOpenOrderFromList(null);
      handleLoadOrderManagementData(foundItem ? foundItem : firstOrder);
      setNavigationStateForNotification(null);
    }
  }, [navigationStateForNotification , isOrderManagementPage , orderManagementData, openOrderFromList])

      
  const handleLoadOrderManagementData = async (item: any) => {
    setShowLoader(true);
    const orderData = await getOrderLines(item.id);
    updateOrderManagementData(item, orderData?.data);

    setTimeout(() => {
      const itemRef = getItemRef(item.id);
      if (itemRef) {
        scrollToItem(itemRef);
      }
    }, 100);
    if (item.id) {
      clickWhenReady(() => getItemRef(item.id) ?? null);
    }
}

useEffect(() => {
  if (clickThisId) {
    clickWhenReady(() => getItemRef(clickThisId) ?? null);
    setClickThisId(null);
  }
}, [clickThisId]);

  
    // Add keyboard event listener for Escape key
    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
          if (event.key === 'Escape') {
              setSelectedSavedSearchIdList([]);
              setLastClickedIndex(null);
              console.log('Escape pressed: Cleared all selections');
          }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => {
          document.removeEventListener('keydown', handleKeyDown);
      };
  }, []);

  useEffect(() => {
    if (isDeletePage) {
      setSelectedSavedSearch(null);
      setSelectedQuote(null);
    }
  }, [isDeletePage]);
  

  useEffect(() => {
    if (isDeletePage && deleteViewFilter) {
      setSelectedSavedSearch(null);
      setSelectedQuote(null);
    }
  }, [deleteViewFilter]);

  const checkIfSearchHasResult = ()=>{
    if(!searchResult || !searchResult.results) return false;
    return Object.values(searchResult.results).some(arr => arr.length > 0);
  }

  const checkIfSearchResultContainsSelectedId = (selectedId: string) =>{
    if(!searchResult || !searchResult.results) return false;
    return Object.values(searchResult.results).some(arr => arr.some((item: any) => item.id === selectedId));
  }

  

  useEffect(() => {
    if (!searchResult || !isSearchMode) return;

    const shouldClearSelection =
      !selectedObject || !checkIfSearchResultContainsSelectedId(selectedObject.id);

    if (shouldClearSelection) { 
      setSelectedSavedSearch(null);
      setSelectedQuote(null);
    }
  }, [searchResult]);

  const getItemRef = (id: string) => {
    if (isSearchMode) {
      // In search mode we can render any source; check all refs
      return itemRefs.current[id] || quoteItemRefs.current[id] || orderItemRefs.current[id];
    }

    if (location.pathname === routes.homePage) return itemRefs.current[id];
    else if (location.pathname === routes.quotePage || location.pathname === routes.createPoPage) return quoteItemRefs.current[id];
    else if (location.pathname === routes.orderManagementPage) return orderItemRefs.current[id];
    else return itemRefs.current[id];
  };

  function triggerReactClick(el: Element) {
    el.dispatchEvent(
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      })
    );
  }

  function clickWhenReady(getEl: () => Element | null, maxTries = 20) {
  let tries = 0;
  const tick = () => {
    const el = getEl();
    if (el) {
      triggerReactClick(el);
      // optional: scroll after a tiny delay to avoid layout thrash
      requestAnimationFrame(() => scrollToItem(el as HTMLElement));
      return;
    }
    if (++tries < maxTries) requestAnimationFrame(tick);
  };
  requestAnimationFrame(tick);
}


useEffect(() => {
  if (!isSearchMode || !selectedObject?.id) return;

  clickWhenReady(() => getItemRef(selectedObject.id) ?? null);
}, [selectedObject]);

  useEffect(() => {
    if (clickThisId) {
      const ref = getItemRef(clickThisId);
      ref?.click();  
      setClickThisId(null);
    }
  }, [clickThisId]);

  const sortedData = useMemo(() => {

  if (isSearchMode) {
    const sr = (searchResult || {});

    const pricingSet  = new Set((sr?.results?.pricing  ?? []).map(x => x?.id).filter(Boolean));
    const quoteSet    = new Set((sr?.results?.quote    ?? []).map(x => x?.id).filter(Boolean));
    const purchaseSet = new Set((sr?.results?.purchase ?? []).map(x => x?.id).filter(Boolean));
    const orderSet    = new Set((sr?.results?.order    ?? []).map(x => x?.id).filter(Boolean));

    const pricingItems: SearchModeItem[] =
      (savedSearchProducts || [])
        .filter((x: any) => pricingSet.has(x.id))
        .map((x: any) => ({ ...x, __source: 'pricing' }));

    const quoteItems: SearchModeItem[] =
      (quoteList || [])
        .filter((x: any) => quoteSet.has(x.id))
        .map((x: any) => ({ ...x, __source: 'quote' }));

    const purchaseItems: SearchModeItem[] =
      (purchasingList || [])
        .filter((x: any) => purchaseSet.has(x.id))
        .map((x: any) => ({ ...x, __source: 'purchase' }));

    const orderItems: SearchModeItem[] =
      (orderManagementData || [])
        .filter((x: any) => orderSet.has(x.id))
        .map((x: any) => ({ ...x, __source: 'order' }));

    const combined: SearchModeItem[] = [
      ...pricingItems,
      ...quoteItems,
      ...purchaseItems,
      ...orderItems,
    ];

    if (!combined.length) return { Today: [] };

    return filterSearchMode(combined, viewFilter);
  }
  // Get appropriate data based on current page
  let data = [];
  if(location.pathname === routes.homePage) data = savedSearchProducts;
  else if(location.pathname === routes.quotePage) data = quoteList;
  else if(isPurchasingPage) data = purchasingList;
  else if(location.pathname === routes.orderManagementPage) data = orderManagementData;
  else if (location.pathname === routes.buyerDeleteOrderPage) data = getDeleteData(deleteViewFilter);
  // If no data, return empty grouped structure
  if (!data || data.length === 0) {
    return { 'Today': [] };
  }

  // Apply filtering based on viewFilter and current route
  switch (viewFilter) {
    case 'newest':
      return filterByNewest(data, location.pathname, userData?.data?.type);
      
    case 'oldest':
      return filterByOldest(data, location.pathname, userData?.data?.type);
      
    case 'a-to-z':
      return filterByAToZ(data, location.pathname, userData?.data?.type);
      
    case 'z-to-a':
      return filterByZToA(data, location.pathname, userData?.data?.type);
      
    case 'highest':
      return filterByHighest(data, location.pathname, userData?.data?.type);
      
    case 'lowest':
      return filterByLowest(data, location.pathname, userData?.data?.type);
      
    default:
      return filterByNewest(data, location.pathname, userData?.data?.type);
  }
}, [  viewFilter,
  isSearchMode,
  searchResult,
  savedSearchProducts,
  quoteList,
  purchasingList,
  orderManagementData,
  location.pathname,
  isPurchasingPage,
  deleteViewFilter,
  getDeleteData,
  buyerDeletedItemsData]);

  useEffect(() => {
    setGrouped(sortedData);
  }, [sortedData]);

const renderSearchModeItem = (item: any, index: number) => {
  const src = (item as SearchModeItem).__source;

  if (src === 'pricing') {
    return (
      <SavedPricingTemplate
        ref={el => { itemRefs.current[item.id] = el ?? null; }}
        key={item.id}
        item={item}
        index={index}
        isSearchMode={true}
        setLastClickedIndex={setLastClickedIndex}
        lastClickedIndex={lastClickedIndex}
        selectedSavedSearchIdList={selectedSavedSearchIdList}
        setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
        handleSaveSearchProducts={handleSaveSearchProducts}
        animatedItems={animatedItems}
        handleCtrlClick={()=>{}}
      />
    );
  }

  if (src === 'quote' || src === 'purchase') {
    return (
      <DraftPoTemplate
        ref={el => { quoteItemRefs.current[item.id] = el?? null; }}
        key={item.id}
        item={item}
        index={index}
        isSearchMode={true}
        animatedItems={animatedItems}
        selectedSavedSearchIdList={selectedSavedSearchIdList}
        setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
        lastClickedIndex={lastClickedIndex}
        setLastClickedIndex={setLastClickedIndex}
        handleCtrlClick={()=>{}}
      />
    );
  }

  if (src === 'order') {
    return (
      <OrderManagementTemplate
        ref={el => { orderItemRefs.current[item.id] = el ?? null; }}
        key={item.id}
        item={item}
        index={index}
        isSearchMode={true}
        animatedItems={animatedItems}
        selectedSavedSearchIdList={selectedSavedSearchIdList}
        setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
        lastClickedIndex={lastClickedIndex}
        setLastClickedIndex={setLastClickedIndex}
        handleCtrlClick={()=>{}}
      />
    );
  }
  return null;
};

const handleSaveSearchProducts = async (priceSearchData: any, saveSearchProductsMutation: any) => {
  try{
     const payload = {
         data: {
             id: priceSearchData?.id.includes(noIdGeneric) ? undefined : priceSearchData?.id,
             title: priceSearchData?.title,
             zipcode: priceSearchData?.zipcode.trim(),
             order_size: String(priceSearchData?.order_size),
             source: "search",
             products: priceSearchData?.products?.length > 0 ? priceSearchData?.products : null
         }
     }
     const response = await saveSearchProductsMutation(payload as any);

     if (response?.data?.data) {
       console.log("Pricing saved successfully");
       setSelectedProductsData([]);
       setSaveFeedbackMap({});
       setFocusSingleProduct({});
       clearLocal(localStorageKeys.instantPriceSearch);
     } else {
       console.error("Failed to save pricing");
     }
  } catch(error){
     console.error('Error saving pricing:', error);
  }
 }

  const handleViewFilterChange = (event: any) => {
    setViewFilter(event.target.value);
  };

  const handleCreateNew = async () => {
    setClickedCreateNewButton(Math.random());
    setSelectedSavedSearchIdList([]);
  }

  useEffect(() => {
    if (clickedCreateNewButton) {
      setSelectedSavedSearchIdList([]);
      setLastClickedIndex(null);
    }
  }, [clickedCreateNewButton]);

  const handleDeleteSelectedSavedSearch = () => {
    // Store the items to be deleted
    let itemsToDelete = [];
    if(isHomePage) itemsToDelete = savedSearchProducts.filter((item: any) => selectedSavedSearchIdList.includes(item.id));
    else if(isQuotePage) itemsToDelete = quoteList.filter((item: any) => selectedSavedSearchIdList.includes(item.id));
    else if(isPurchasingPage) itemsToDelete = purchasingList.filter((item: any) => selectedSavedSearchIdList.includes(item.id));
    else itemsToDelete = [];
    setDeletedItems(itemsToDelete);
    setSelectedSavedSearch(null);
    setShortListedSearchProductsData([]);
    setFilterShortListedSearchProductsData([]);
    setSelectedQuote(null);
    
    // Remove items from the list temporarily
    if(isHomePage){
      const updatedProducts = savedSearchProducts.filter((item: any) => 
        !selectedSavedSearchIdList.includes(item.id)
      );
      setSavedSearchProducts(updatedProducts);
    }
    else if(isQuotePage){
      const updatedProducts = quoteList.filter((item: any) => 
        !selectedSavedSearchIdList.includes(item.id)
      );
      setQuoteList(updatedProducts);
    } else if(isPurchasingPage){
      const updatedProducts = purchasingList.filter((item: any) => 
        !selectedSavedSearchIdList.includes(item.id)
      );
      setPurchasingList(updatedProducts);
    }
    
    // Clear the selection
    setSelectedSavedSearchIdList([]);
    
    // Show undo button and start countdown
    setShowUndoDelete(true);
    setUndoCountdown(10);
    
    // Start countdown timer
    const timer = setInterval(() => {
      setUndoCountdown((prev) => {
        if (prev <= 1) {
          // Time's up, actually delete the items
          handleConfirmDelete(itemsToDelete);
          setShowUndoDelete(false);
          setDeletedItems([]);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    setUndoTimer(timer);
  };

  const handleUndoDelete = () => {
    // Restore the deleted items
    restoreDeletedItems();
    
    // Clear undo state
    setShowUndoDelete(false);
    setDeletedItems([]);
    setUndoCountdown(10);
    
    // Clear the timer
    if (undoTimer) {
      clearInterval(undoTimer);
      setUndoTimer(null);
    }
  };

  const handleConfirmDelete = async (itemsToDelete: any[]) => {
    try {
      if(itemsToDelete.length === 0) {
        restoreDeletedItems();
        return;
      }
      // Call your delete API here
      const payload = {
        data: itemsToDelete.map((item: any) => item.id)
      }
      let response;
      if(isHomePage) response = await deleteSearchProductsMutation(payload as any);
      else if(isQuotePage || isPurchasingPage) response = await cancelDraftPo(payload as any);

      if (response?.data?.data) {
        console.log("Items deleted successfully");
        setClickedCreateNewButton(Math.random());
      } else {
        console.error("Failed to delete items");
        restoreDeletedItems();
      }
      // Example API call:
      // await deleteSavedSearchAPI(itemsToDelete);
      
      // The items are already removed from the UI state
      // so no additional UI updates needed
    } catch (error) {
      console.error('Error deleting items:', error);
      // Optionally restore items on error
      restoreDeletedItems();
    }
  };

  const restoreDeletedItems = () => {
    if(isHomePage){
      const restoredProducts = [...savedSearchProducts, ...deletedItems];
      setSavedSearchProducts(restoredProducts);
    }
    else if(isQuotePage){
      const restoredProducts = [...quoteList, ...deletedItems];
      setQuoteList(restoredProducts);
    } else if(isPurchasingPage){
      const restoredProducts = [...purchasingList, ...deletedItems];
      setPurchasingList(restoredProducts);
    }
  }

  // Cleanup timer on component unmount
  useEffect(() => {
    return () => {
      if (undoTimer) {
        clearInterval(undoTimer);
      }
    };
  }, [undoTimer]);

  const handleCtrlClick = (item: any, index: number, onCtrlClickCallback: (currentSelectedIds: any[], updatedIds: any[]) => void) => {
    const currentSelectedIds = [...selectedSavedSearchIdList];
    const itemId = item.id;
    let updatedIds: any[] = [];

    if (currentSelectedIds.includes(itemId)) {
      // Remove item from selection
      updatedIds = currentSelectedIds.filter(id => id !== itemId);
      setSelectedSavedSearchIdList(updatedIds);
    } else {
      // Add item to selection
      updatedIds = [...currentSelectedIds, itemId];
      setSelectedSavedSearchIdList(updatedIds);
    }
    onCtrlClickCallback(currentSelectedIds, updatedIds);
    setLastClickedIndex(index);
  };

  return (
    <div 
      className={clsx(styles.listTab, selectedQuote && styles.dimListTab, isDisableLeftPanel && styles.disableLeftPanel)}
      data-hover-video-id={isSearchMode ? 'search-left-panel' : isOrderManagementPage ? ( isSeller ? 'seller-om-left-panel' : 'om-left-panel'): isQuotePage ? 'quoting-left-panel' : isPurchasingPage ? 'purchase-left-panel' : isHomePage ? 'search-left-panel' : ''}
    >
      <div className={styles.topSectionBtnLeft}>
        <div className={styles.listActionContainer}>
          {!isOrderManagementPage && !isDeletePage &&
          (
          <div className={clsx(styles.createNew)} style={{display: "flex"}}>
            {((selectedSavedSearch || selectedQuote) && !isSearchMode) &&
              <button 
                disabled={isDisableLeftPanel}
                data-hover-video-id={isQuotePage ? 'quoting-create-new' : isPurchasingPage ? 'purchase-create-new' : isHomePage ? 'price-search-create-new' : ''}
                className={clsx(styles.createNewBtnHover, styles.createNewBtnAnimation, styles.initialPositionForAnimation,styles.slideInAnimation5)} onClick={handleCreateNew}>
                Create New
                <div className={styles.createButtonIcon}>
                  <CreateNewPluseIconHover />
                </div>
              </button>
            }
            <button className={styles.createNewBtn}>
              Create New
              <div className={styles.createButtonIcon}>
                <CreateNewPluseIcon />
              </div>
            </button>

            
          </div>
          )}
          {selectedQuote?.bom_id && !isDeletePage &&
          <button 
              className={styles.backToReviewBtn}
              onClick={(e) => {
                setAllBoxes([]);
                setPdfFile(null);
                setPdfUrl('');
                setPdfFileName('');
                setBomUploadID(selectedQuote.bom_id);
                const {
                  delivery_date,
                  shipping_details,
                  order_type,
                  buyer_internal_po,
                  delivery_date_offset,
                  id
              } = selectedQuote;
                setUploadBomInitialData({
                  delivery_date,
                  shipping_details,
                  order_type,
                  buyer_internal_po,
                  delivery_date_offset,
                  id
              })
                navigatePage(location.pathname, {path: routes.bomExtractor+'?isBack=true'})
                console.log("back to review button clicked!")
              }}
              disabled={!selectedQuote || !selectedQuote?.bom_id}
            > <LeftSideArrow/>Back to Review</button>
          }
          {selectedSavedSearchIdList.length > 1 &&
            <div className={styles.deleteContainer} onClick={handleDeleteSelectedSavedSearch}>
              <span><DeleteIcon /></span> <span>x {selectedSavedSearchIdList.length}</span>
            </div>
          }
          {showUndoDelete && (
            <button className={styles.undoContainer} onClick={handleUndoDelete}>
              Undo Delete
            </button>
          )}

        </div>
      </div>
      <div className={styles.titleSection}>
        <span>
          {isQuotePage ? <span className={clsx(styles.quoting, styles.title)}>QUOTING</span> : 
           isPurchasingPage ? <span className={clsx(styles.purchasing, styles.title)}>PURCHASING</span> : 
           isOrderManagementPage ? <span className={clsx(styles.order, styles.title)}>ORDER MANAGEMENT</span> :
           isDeletePage ? <span className={clsx(styles.deletedItems, styles.title)}>DELETED ITEMS</span> :
           isSearchMode ? <span className={clsx(styles.searchResults, styles.title)}>SEARCH RESULTS</span> :
           <span className={clsx(styles.instantPriceSearch, styles.title)}>Instant Price Search</span>}
        </span>
      </div>
      <div className={styles.filterSection}>
        <div className={styles.filterSectionLeft}>
         {!isDeletePage &&  <Select
            value={viewFilter}
            onChange={handleViewFilterChange}
            open={selectOpen}
            IconComponent={DropdownArrow}
            onOpen={() => setSelectOpen(true)}
            onClose={() => setSelectOpen(false)}
            className={clsx('selectDropdown','instantPriceSearchDropdown')}
            MenuProps={
              {
                classes: {
                  paper: styles.dropDownBG
                },
              }
            }
          >
            <MenuItem value="newest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Newest to Oldest</span>
              </div>
            </MenuItem>
            <MenuItem value="oldest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Oldest to Newest</span>
              </div>
            </MenuItem>
            <MenuItem value="a-to-z" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>A to Z</span>
              </div>
            </MenuItem>
            <MenuItem value="z-to-a" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Z to A</span>
              </div>
            </MenuItem>
            <MenuItem value="highest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Highest to Lowest</span>
              </div>
            </MenuItem>
            <MenuItem value="lowest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Lowest to Highest</span>
              </div>
            </MenuItem>
          </Select>}

          {isDeletePage && <Select value={deleteViewFilter} onChange={(e) => setDeleteViewFilter(e.target.value)} open={deleteSelectOpen}
            IconComponent={DropdownArrow}
            onOpen={() => setDeleteSelectOpen(true)}
            onClose={() => setDeleteSelectOpen(false)}
            className={clsx('selectDropdown','instantPriceSearchDropdown')}
            MenuProps={
              {
                classes: {
                  paper: styles.dropDownBG
                },
              }
            }>
              {
                import.meta.env.VITE_CLIENT_ID === bryzosClientId && ( 
                <MenuItem value="instant_price_search" className={styles.menuItem}>
                  <div className={styles.menuItemContent}>
                    <span>Instant Price Search</span>
                  </div>
                </MenuItem>
                )

              }
            <MenuItem value="quoting" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Quoting</span>
              </div>
            </MenuItem>
            <MenuItem value="purchasing" className={styles.menuItem}>
            <div className={styles.menuItemContent}>
                <span>Purchasing</span>
              </div></MenuItem>
             
       
            </Select>}
        </div>
       
      </div>
      <div className={styles.listSection}>
        <div ref={listTabScrollRef} className={styles.savedSearchListContainer}>
        {Object.keys(grouped).length > 0 ?
          Object.entries(grouped).map(([label, items]: any, index: number) => (
            <div key={index+label} className={styles.searchContainer}>
              <p className={styles.searchLabel}>{label}</p>
                {items.length > 0 ? items.map((item: any, index: number) => {
                  return (
                    <div className={styles.searchItemContainerMain} style={{ cursor: 'pointer' }} key={item.id}>
                      {isSearchMode
                        ? renderSearchModeItem(item, index)
                        : (
                          <>
                            {isHomePage && (
                              <SavedPricingTemplate
                                ref={el => { itemRefs.current[item.id] = el?? null; }}
                                key={item.id}
                                item={item}
                                index={index}
                                setLastClickedIndex={setLastClickedIndex}
                                lastClickedIndex={lastClickedIndex}
                                selectedSavedSearchIdList={selectedSavedSearchIdList}
                                setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
                                handleSaveSearchProducts={handleSaveSearchProducts}
                                animatedItems={animatedItems}
                                handleCtrlClick={handleCtrlClick}
                              />
                            )}

                            {(isQuotePage || isPurchasingPage) && (
                              <DraftPoTemplate
                                ref={el => { quoteItemRefs.current[item.id] = el?? null; }}
                                key={item.id}
                                item={item}
                                index={index}
                                animatedItems={animatedItems}
                                selectedSavedSearchIdList={selectedSavedSearchIdList}
                                setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
                                lastClickedIndex={lastClickedIndex}
                                setLastClickedIndex={setLastClickedIndex}
                                handleCtrlClick={handleCtrlClick}
                              />
                            )}

                            {isOrderManagementPage && (
                              <OrderManagementTemplate
                                ref={el => { orderItemRefs.current[item.id] = el?? null; }}
                                key={item.id}
                                item={item}
                                index={index}
                                animatedItems={animatedItems}
                                selectedSavedSearchIdList={selectedSavedSearchIdList}
                                setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
                                lastClickedIndex={lastClickedIndex}
                                setLastClickedIndex={setLastClickedIndex}
                                handleCtrlClick={handleCtrlClick}
                              />
                            )}

                            {isDeletePage && item.order_type === 'INSTANT_PRICING' ? (
                              <SavedPricingTemplate
                                ref={el => { itemRefs.current[item.id] = el?? null; }}
                                key={item.id}
                                item={item}
                                index={index}
                                setLastClickedIndex={setLastClickedIndex}
                                lastClickedIndex={lastClickedIndex}
                                selectedSavedSearchIdList={selectedSavedSearchIdList}
                                setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
                                handleSaveSearchProducts={handleSaveSearchProducts}
                                animatedItems={animatedItems}
                                handleCtrlClick={handleCtrlClick}
                              />
                            ) : (isDeletePage && (item.order_type === 'QUOTE' || item.order_type === 'PO')) ? (
                              <DraftPoTemplate
                                ref={el => { quoteItemRefs.current[item.id] = el?? null; }}
                                key={item.id}
                                item={item}
                                index={index}
                                animatedItems={animatedItems}
                                selectedSavedSearchIdList={selectedSavedSearchIdList}
                                setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
                                lastClickedIndex={lastClickedIndex}
                                setLastClickedIndex={setLastClickedIndex}
                                handleCtrlClick={handleCtrlClick}
                              />
                            ) : null}
                          </>
                        )}
                    </div>
                  );
                })
                  :
                  <div className={styles.noDataContainer}>
                    <span className={styles.noDataMessage}>
                        {location.pathname === routes.searchResult ? "No search results found" : 
                        isDeletePage ? "No deleted items found" : 
                        location.pathname === routes.homePage ? "Your Instant Pricing activity will be saved here" : 
                        isPurchasingPage ? "Your Purchasing activity will be saved here" : 
                        isOrderManagementPage ? "Your Order Management activity will be saved here" : "Your Quote activity will be saved here"}
                    </span>
                  </div>
                }
              
            </div>
          ))
          :
          <div className={styles.noDataContainer}>
            <span className={styles.noDataMessage}>No Data Found</span>
          </div>
        }
        </div>
      </div>
    </div>
  )
}

export default ListTab
