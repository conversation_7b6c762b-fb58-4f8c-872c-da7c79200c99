import React from 'react';
import { useHoverVideoStore } from '../component/LeftPanel/HoverVideoStore';
import { useRightWindowStore } from '../pages/RightWindow/RightWindowStore';
import HoverVideoPlayer from '../component/HoverVideoPlayer/HoverVideoPlayer';

export const useHoverVideo = () => {
  const { isHoverVideoEnabled, setVideoToolTipData, VideoToolTipConfig } = useHoverVideoStore();
  const { setToolTipVideoComponent, toolTipVideoComponent } = useRightWindowStore();


  const handleHoverVideoMouseEnter = (videoId: string) => {
    const config = VideoToolTipConfig?.[videoId];
    const { isHoverVideoEnabled } = useHoverVideoStore.getState();
    if(config && isHoverVideoEnabled){
      setVideoToolTipData(config);
    }
    if (isHoverVideoEnabled && config && !toolTipVideoComponent) {
      setToolTipVideoComponent(
        React.createElement(HoverVideoPlayer)
      );
    }
  };

  const handleHoverVideoMouseLeave = () => {
    setVideoToolTipData(null);
    setToolTipVideoComponent(null);

  };

  return {
    handleHoverVideoMouseEnter,
    handleHoverVideoMouseLeave,
    isHoverVideoEnabled
  };
}; 