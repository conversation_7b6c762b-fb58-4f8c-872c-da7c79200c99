import React from 'react';
import { Navigate } from 'react-router-dom';
import { isModuleEnabled, getFirstEnabledRoute } from '../../helper';
import { routes } from '../../common';

interface ProtectedRouteProps {
  children: React.ReactNode;
  moduleId: string;
  userRole?: string;
  fallbackRoute?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  moduleId, 
  userRole,
  fallbackRoute 
}) => {
  // Check if the module is enabled for this client
  if (!isModuleEnabled(moduleId)) {
    // If module is disabled, redirect to fallback route or first enabled route
    const redirectTo = fallbackRoute || getFirstEnabledRoute() || routes.loginPage;
    return <Navigate to={redirectTo} replace />;
  }

  // If module is enabled, render the component
  return <>{children}</>;
};

export default ProtectedRoute;
