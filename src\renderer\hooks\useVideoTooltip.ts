import { useQuery } from "@tanstack/react-query";
import { reactQuery<PERSON>eys } from "../common";
import axios from "axios";
import { useGlobalStore } from "@bryzos/giss-ui-library";

const useVideoTooltip = () => {
  const userData = useGlobalStore.getState().userData;
  return useQuery(
    [reactQueryKeys.getVideoTooltip],
    async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/video-mappings/hover-video`
        );

        if (response.data && response.data.data) {
          if (
            typeof response.data.data === "object" &&
            "err_message" in response.data.data
          ) {
            throw new Error(response.data.data.err_message);
          } else {
            return response.data.data;
          }
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error?.message ?? error);
      }
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      staleTime: 0,
      enabled: !!userData?.data
    }
  );
};

export default useVideoTooltip;