.headerInfoContainer {
    padding: 16px;
    position: relative;
    background-color: #191a20;
}

.calendarOpenOverlay {
    width: 100%;
    height: 100%;
    pointer-events: none;
    -webkit-backdrop-filter: blur(8.9px);
    backdrop-filter: blur(8.9px);
    background-color: rgba(76, 76, 76, 0.09);
    position: absolute;
    z-index: 999;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
}

.isCalendarOpenDiabledInput {
    // background-image: linear-gradient(79deg, #0f0f14 50%, #393e47 135%);
    input,
    .deliverToContainer,
    .addressInputs,
    .uploadBillContainer,
    .radioGroupContainer,
    .deliverByButton1,
    .deliverByButton2 {
        pointer-events: none;
    }
}


.createPoHeaderInfoGrid {
    display: flex;
    justify-content: space-between;

    .leftGridHeader {
        width: 45%;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 12px;
        display: flex;
        flex-direction: column;
        row-gap: 12px;

        .col1 {
            width: 100%;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: #fff;
            display: flex;
            gap: 8px;

            .poNameLabel {
                opacity: 0.5;
                display: grid;
                grid-template-columns: 89px auto;
                column-gap: 3px;
                justify-content: space-between;
                white-space: nowrap;

            }

            .pOInputValue {
                display: flex;
                flex-direction: column;
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.2;
                letter-spacing: 0.56px;
                text-align: left;
                color: #fff;
                &.disputeMode{
                    color: #ffb800;
                }
            }

            &.deliverByContainer {
                width: 100%;
                position: relative;

                button {
                    height: 20px;
                    font-family: Inter;
                    font-size: 12px;
                    border-radius: 4px;

                    span {
                        height: 100%;
                        border-radius: 0px;
                    }
                }
            }
        }

    }

    .rightGridHeader {
        width: 25.39%;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 16px 0px;
        display: flex;
        align-items: center;
        justify-content: center;

        .actionButtonsContainer{
            display: flex;
            gap: 12px;
            margin-left: auto;
            .actionButtons{
                padding: 10px 38px;
                 border-radius: 500px;
                background-color: rgba(255, 255, 255, 0.04);
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: rgba(255, 255, 255, 0.5);
                transition: all 0.2s ease;
                &:hover{
                    color: #fff;
                }
            }
        }

        .poCreateDateContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 16px;

            .uploadLabel1 {
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: center;
                color: #c3c4ca;
            }

            .uploadLabel2 {
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.2;
                letter-spacing: 0.48px;
                text-align: center;
                color: #fff;
            }

            .uploadLabel3 {
                font-family: Inter;
                font-size: 12px;
                font-weight: 200;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.2;
                letter-spacing: 0.48px;
                text-align: center;
                color: #fff;
            }
        }

        .updatePricingContainer {
            button {
                position: relative;
                font-family: Syncopate;
                font-size: 14px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.56px;
                text-align: center;
                background: none;
                border: none;
                cursor: pointer;
                transition: all 0.4s ease;
                width: 100%;
                z-index: 1;

                &:disabled {
                    cursor: not-allowed;

                    .buttonText {
                        width: 100%;
                        height: 40px;
                        flex-grow: 0;
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                        align-items: flex-start;
                        padding: 12px 0 10px;
                        opacity: 0.1;
                        border-radius: 5000px;
                        background-color: #222329;
                        color: #fff;
                        font-family: Syncopate;
                        font-size: 14px;
                        font-weight: bold;
                        font-stretch: normal;
                        font-style: normal;
                        line-height: 1.3;
                        letter-spacing: -0.56px;
                        text-align: center;
                    }
                }
            }

            // Enabled .buttonText
            button:not(:disabled) .buttonText {
                width: 100%;
                height: 40px;
                flex-grow: 0;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: flex-start;
                padding: 12px 0 10px;
                border-radius: 5000px;
                background-color: #ffb800;
                overflow: visible;
                position: relative;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    inset: -6px;
                    border: 8px solid rgba(255, 184, 0, 0.3);
                    border-radius: inherit;
                    animation: pulseOut 2s ease-out infinite;
                    opacity: 0;
                }

                &::after {
                    animation-delay: 1.2s;
                }
            }

            @keyframes pulseOut {
                0% {
                    transform: scale(1);
                    opacity: 1;
                }

                100% {
                    transform: scale(1.1);
                    opacity: 0;
                }
            }
        }

        .uploadBillContainer {
            width: 100%;
            height: 100%;
            // min-height: 180px;
            flex-grow: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 8px;
            padding: 0;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            cursor: pointer;
            transition: all 0.2s ease;

            .uploadIcon2 {
                display: none;
            }

            &.disabled {
                cursor: not-allowed;
                // opacity: 0.5;

                &:hover {
                    background: rgba(255, 255, 255, 0.04);

                    .uploadLabel {
                        font-weight: normal;
                        color: #616575;
                    }

                    .uploadIcon2 {
                        display: none;
                    }

                    .uploadIcon1 {
                        display: block;
                    }
                }
            }

            &:hover {
                background: #fff;

                .uploadLabel {
                    color: #1b1b21;
                    font-weight: bold;
                }

                .uploadIcon2 {
                    display: block;
                }

                .uploadIcon1 {
                    display: none;
                }
            }

            &:focus-visible {
                outline: none;
                background: #fff;

                .uploadLabel {
                    color: #1b1b21;
                    font-weight: bold;
                }

                .uploadIcon2 {
                    display: block;
                }

                .uploadIcon1 {
                    display: none;
                }
            }

            .uploadIcon {
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .uploadLabel {
                font-family: Syncopate;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.2;
                letter-spacing: 0.56px;
                text-align: center;
                color: #616575;
            }
        }
    }






}
.pointRight {
    position: absolute;
    left:-22px;
    top: 50%;
    transform: translateY(-50%);
}
.deliveryDateContainer {
    margin-top: 12px;
    padding: 12px 12px 12px 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: #fff;
    position: relative;
    span {
        color: #fff;
        margin-left: 7px;
    }
    .disputeHeaderInfoGrid {
        display: flex;
        justify-content: space-between;
        column-gap: 12px;
    
    
        .leftGridHeader {
            min-width: 50%;
            border-radius: 12px;
            padding:16px 12px 12px 12px;
            display: flex;
            flex-direction: column;
            gap: 12px;
    
            .col1 {
                width: 100%;
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1;
                letter-spacing: 0.56px;
                text-align: left;
                color: #fff;
                display: flex;
                gap: 7px;
                align-items: center;
    
                .poNameLabel {
                     color: rgba(255, 255, 255, 0.5);
                    // width: 89px;
                    display: flex;
                    justify-content: space-between;
                    white-space: nowrap;
    
                }
    
                .pOInputValue {
                    display: flex;
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.2;
                    letter-spacing: 0.56px;
                    text-align: left;
                    color: #fff;
                    flex-direction: column;
                    width: 100%;
                    gap: 8px;
                    .disputeItem{
                        display: flex;
                        gap: 37px;
                        label{
                            min-width: 80px;
                        }
                    }
                }
    
                &.deliverByContainer {
                    width: 100%;
                    position: relative;
    
                    button {
                        height: 20px;
                        font-family: Inter;
                        font-size: 12px;
                        border-radius: 4px;
    
                        span {
                            height: 100%;
                            border-radius: 0px;
                        }
                    }
                }
            }
    
        }
    
        .rightGridHeader {
            width: 100%;
            border-radius: 12px;
            padding: 12px;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            flex-direction: row;
            gap: 12px;
            position: relative;
            &.actionBtnContainer{
                align-items: center;
            }
            .calendarWrapper.calendarWrapper{
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
            }
    
            .poNameLabel {
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1;
                letter-spacing: 0.56px;
                text-align: left;
                color: rgba(255, 255, 255, 0.5);
                span{
                    color: #fff;
                    text-transform: uppercase;
                    margin-left: 7px;
                }
                &:nth-child(2){
                   text-transform: uppercase;
                }
            }
            .actionButtonsContainer{
                display: flex;
                gap: 12px;
                margin-left: auto;
                .actionButtons{
                    min-width: 112px;
                    padding: 10px 12px;
                    border-radius: 500px;
                    background-color: rgba(255, 255, 255, 0.04);
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: normal;
                    text-align: center;
                    color: rgba(255, 255, 255, 0.5);
                    transition: all 0.2s ease;
                    flex: 1;
                    &:hover{
                        color: #fff;
                    }
                }
            }
        }
    
    
    }
}

.poInputMain {
    position: relative;

    input {
        width: 100%;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 4px 8px;
        height: 20px;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
        flex: 1;
    }

    .hasValue {
        position: relative;

        &:focus-within {
            overflow: hidden;
            z-index: 0;
            border-radius: 12px;

            &::before {
                content: '';
                position: absolute;
                inset: 0;
                border-radius: inherit;
                padding: 1px;
                background: linear-gradient(to bottom right, #1a1b20 61%, #fff 294%);

                -webkit-mask:
                    linear-gradient(#fff 0 0) content-box,
                    linear-gradient(#fff 0 0);
                -webkit-mask-composite: xor;
                mask-composite: exclude;

                background-clip: border-box;
                z-index: -1;
                pointer-events: none;
            }


        }

        .pOInput {
            &:focus {
                border: 0px solid transparent;
                background: transparent;
            }
        }
    }

}

 .pOInput {
    border: 0px solid transparent;
    transition: all 0.2s ease;
    width: 100%;

    &:focus {
      border: 0px solid transparent;
      color: #fff;
      background-image: linear-gradient(126deg, #1c40e7 -20%, #16b9ff 114%), linear-gradient(286deg, #fff 116%, #1a1b20 30%);
      box-shadow: inset 3px 3px 7px rgba(0, 0, 0, 1);
      &::placeholder {
        color: #fff;
      }
    }

  }


.deliverToContainer {
    width: 100%;
    height: 100px;
    padding: 4px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.04);

}

.autocompleteContainer {
    position: relative;
    width: 100%;

    :global(.MuiAutocomplete-root) {
        width: 100%;

        :global(.MuiAutocomplete-inputRoot) {
            -webkit-box-flex-wrap: nowrap;
            -ms-flex-wrap: nowrap;
            flex-wrap: nowrap;
        }

        .MuiAutocomplete-inputRoot.MuiAutocomplete-inputRoot {
            -webkit-box-flex-wrap: nowrap;
            -ms-flex-wrap: nowrap;
            flex-wrap: nowrap;
        }

        .MuiAutocomplete-noOptions.MuiAutocomplete-noOptions {
            display: none;

        }
    }
}

.muiAutocompleteTextField.muiAutocompleteTextField {
    width: 100%;
    height: 20px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.04);

    input {
        padding: 0px !important;
    }


    :global(.MuiInputBase-root) {
        height: 20px;
        width: 100%;
        border: 1px solid transparent;
        border-radius: 8px;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
        padding: 0 8px;
        transition: all 0.1s ease;

        &:hover {
            border-color: transparent;
        }

        &.Mui-focused {
            border-color: transparent;
            color: #fff;
            box-shadow: none;
        }

        &.Mui-error {
            box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
            border: 0px;
            background-image: linear-gradient(138deg, var(--error-bg-dark) -109%, var(--error-bg-light) 87%), linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
            background-origin: border-box;
            background-clip: border-box, border-box;
            color: #fff;

            &.Mui-focused {
                color: #fff;
                box-shadow: none;
            }
        }
    }

    :global(.MuiInputBase-input) {
        height: 100%;
        padding: 0;
        color: inherit;
        font-family: inherit;
        font-size: inherit;
        letter-spacing: inherit;
        border: none;
        outline: none;
        background-color: transparent;
        caret-color: #1fbbff;

        &::placeholder {
            font-family: Syncopate;
            font-size: 12px;
            letter-spacing: 0.56px;
            color: #616575;
            opacity: 1;
        }

        &:focus {
            outline: none;
            color: #1fbbff;
        }

        &.Mui-error {
            color: #fff;

            &::placeholder {
                color: white;
            }
        }
    }

    :global(.MuiInputAdornment-root) {
        color: #616575;
    }

    :global(.MuiFormHelperText-root) {
        display: none;
    }

    :global(.MuiInputLabel-root) {
        display: none;
    }

    :global(.MuiOutlinedInput-notchedOutline) {
        border: none;
    }
}



.autocompleteDropdown.autocompleteDropdown {
    max-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    gap: 8px;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    background-color: rgba(128, 130, 140, 0.28);
    margin-top: 4px;

    ul {
        &::-webkit-scrollbar {
            width: 6px;
        }

        li {
            margin-right: 3px;
            padding: 6px 16px;
            border-radius: 8px;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            background-color: transparent;

            &:hover {
                background-color: rgba(255, 255, 255, 0.2);
                color: #fff;
            }

            &[aria-selected='true'] {
                background-color: transparent !important;
                box-shadow: unset;
                color: #fff;

                &:hover {
                    background-color: rgba(255, 255, 255, 0.2) !important;
                    color: #fff;
                }
            }
        }
    }

    &.autocompleteDBA {
        ul {
            li {
                font-size: 16px;
                padding: 8px 16px;
            }
        }
    }
}

.stateZipContainer {
    display: flex;
    gap: 4px;
    margin-top: 4px;

    span {
        &:nth-child(1) {
            flex: 0 45%;
        }

        &:nth-child(2) {
            flex: 0 55%;
        }
    }
}

.addressInputsCol2 {
    position: relative;

    input {
        width: 100%;
        height: 20px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 4px 8px 4px 8px;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
        border: none;
    }

    .shape1 {
        width: 14px;
        height: 14px;
        position: absolute;
        transform: rotate(-450deg);
        top: -6px;
        left: -12px;
        svg{
            filter: opacity(0.28);
        }
    }

    .shape2 {
        width: 13px;
        height: 14px;
        position: absolute;
        transform: rotate(180deg);
        top: -1px;
        right: -5.8px;
        svg{
            filter: opacity(0.28);
        }

    }

    &.selectShade {
        background-color: #3e3f47;
        height: 26px;
        top: -6px;
        position: relative;
        border-radius: 0px 0px 8px 8px;
    }


    .stateWrapper {
        height: 34px;
        border-radius: 8px;
        background-color: #3e3f47;
        display: flex;
        align-items: center;
        padding: 4px 0px 0px 5px;

        svg {
            position: absolute;
            right: 2px;
            top: 8px;
        }

        input {
            width: 63px;
            height: 24px;
            padding: 5px 2.4px 5px 4.5px;
            border-radius: 6px;
            background-color: #111217;
            font-family: Syncopate;
            font-size: 14px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: #459fff;

            &::placeholder {
                color: rgba(97, 101, 117, 0.5);
            }
        }
    }
}

.deliverToLabel {
    display: flex;
    flex-direction: column;
    row-gap: 4px;
}

.addressInputs {
    width: 100%;
    height: 20px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.04);
    padding: 4px 8px 4px 8px;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.48px;
    text-align: left;
    color: #fff;
}

.lastAddressFiled1 {
    .addressInputsCol1 {
        width: 100%;
        height: 20px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 4px 8px 4px 8px;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
    }

    .stateZipContainer1 {
        display: flex;
        gap: 4px;
        margin-top: 4px;

        p {
            height: 20px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.04);
            padding: 4px 8px 4px 8px;
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.48px;
            text-align: left;
            color: #fff;

            &:nth-child(1) {
                flex: 0 45%;
            }

            &:nth-child(2) {
                flex: 0 55%;
            }
        }
    }
}

.addressInputsCol3 {
    input {
        width: 100%;
        height: 20px;
        padding: 4px 8px 4px 8px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.04);
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
    }
}

.stateDropdown {
    .stateWrapper {
        input {
            border: none;
        }
    }
}

.strikeThroughText {
    text-decoration: line-through;
    color: #71737f;
}

.disputeCalendarPopover.disputeCalendarPopover {
      box-shadow: none;
      background-color: transparent;
      margin-top: 3px;
  }

.scrollDisputeItem {
    max-height: 85px;
    overflow: auto;
     smooth-scroll-behavior: auto;

    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background: #8b91a6;
        border-radius: 24px;
    }
}
