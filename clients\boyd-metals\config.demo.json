{"clientId": "boyd-metals", "clientName": "Boyd Metals", "protocolUrl": "boyd-metals-giss", "electronUpdateBranch": "BOYD-METALS-DEMO", "branch": "DEMO", "webpage": "http://d1v51cyd6b2hvn.cloudfront.net/", "updateUrl": "https://extended-widget-ui-hazel.vercel.app", "rendererVersionCheckUrl": "https://boydmetalsdemobryzoswidget.com/widget-service/external-affairs/render/version", "internetCheckURL": "https://boydmetalsdemobryzoswidget.com/widget-service/reference-data/homepage", "pusherNotification": {"pusher": {"key": "6f3fa7e6f42d93bcf5d1", "cluster": "mt1"}, "channels": ["extended-pricing-widget-notification", "private-channel-", "extended-pricing-widget-buyer-notification", "extended-pricing-widget-seller-notification"], "authUrl": "https://boydmetalsdemobryzoswidget.com/notification-service", "channelEvents": {"privateEvents": ["NOTIFICATION_BUYER_CHECKOUT", "NOTIFICATION_SELLER_CHECKOUT", "NOTIFICATION_BNPL_APPROVAL_STATUS", "NOTIFICATION_BUYER_INVOICE_READY", "NOTIFICATION_SELLER_FUNDING_INITIATED", "NOTIFICATION_BUYER_SALES_TAX_REMINDER", "NOTIFICATION_SELLER_ORDER_PREVIEW", "NOTIFICATION_SELLER_ORDER_CLAIM", "NOTIFICATION_BUYER_ORDER_CANCEL", "NOTIFICATION_BUYER_ORDER_LINE_CANCEL", "NOTIFICATION_SELLER_ORDER_CANCEL", "NOTIFICATION_SELLER_ORDER_LINE_CANCEL", "NOTIFICATION_BUYER_NEVER_PURCHASED", "NOTIFICATION_SELLER_NEVER_CLAIMED", "CUSTOM_NOTIFICATION", "NOTIFICATION_CHAT_USER_MESSAGE_RECEIVED", "NOTIFICATION_CHAT_MODERATOR_MESSAGE_RECEIVED", "NOTIFICATION_RECEIVED", "NOTIFICATION_PRICE_CHANGES", "NOTIFICATION_PRODUCT_CHANGES"], "publicEvents": ["NOTIFICATION_PUBLIC", "CUSTOM_NOTIFICATION", "NOTIFICATION_RECEIVED", "NOTIFICATION_PRICE_CHANGES", "NOTIFICATION_PRODUCT_CHANGES"], "buyerEvents": ["CUSTOM_NOTIFICATION", "NOTIFICATION_RECEIVED", "NOTIFICATION_PRICE_CHANGES", "NOTIFICATION_PRODUCT_CHANGES"], "sellerEvents": ["NOTIFICATION_SELLER_ORDER_PREVIEW", "NOTIFICATION_SELLER_ORDER_CLAIM", "NOTIFICATION_SELLER_NEW_BUYER_ADDED", "CUSTOM_NOTIFICATION", "NOTIFICATION_RECEIVED", "NOTIFICATION_PRICE_CHANGES", "NOTIFICATION_PRODUCT_CHANGES"]}}, "commonAppEventsofPusher": {"privateEvents": {"customNotification": "CUSTOM_NOTIFICATION", "userUiUploadLog": "USER_UI_UPLOAD_LOG"}, "publicEvents": {"customNotification": "CUSTOM_NOTIFICATION"}, "buyerEvents": {"customNotification": "CUSTOM_NOTIFICATION"}, "sellerEvents": {"customNotification": "CUSTOM_NOTIFICATION"}}}