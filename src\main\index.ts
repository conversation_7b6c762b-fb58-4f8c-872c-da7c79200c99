// @ts-nocheck
import { shell, app, BrowserWindow, session, ipcMain, Tray, Menu, nativeImage, screen, dialog, powerMonitor, nativeTheme, clipboard  } from 'electron';
import path from 'path';
import { pdfMakeData, routes } from "../renderer/common";
import electronLocalshortcut from 'electron-localshortcut';
import { checkRendererVersion, isDev, urlMasks } from './helper';
import config from './config';
import Store from 'electron-store';
import pusherInit from './pusher';
import os from 'os';
import axios from 'axios';
import PdfGenerator from './PdfGenerator';
import { machineIdSync} from 'node-machine-id';
import log from 'electron-log/main';
import fs from 'fs';
import util from 'util';
import { Database } from '@journeyapps/sqlcipher';
import semver from 'semver';
import ExcelJS from 'exceljs';

import { Notification } from 'electron';
import updater from './update';

const dockIconNameDarkMac = 'dock-icon-mac-os-dark-1024.png';
const dockIconNameLightMac = 'dock-icon-mac-os-light-1024.png';

const trayIconNameDarkMac = 'tray-icon-mac-os-dark-16.png';
const trayIconNameLightMac = 'tray-icon-mac-os-light-16.png';

const trayIconNameDarkWin = 'tray-win-white-icon-for-dark-mode.png';
const trayIconNameLightWin = 'tray-win-black-icon-for-light-mode.png';
export let rendererVersion;

const { webpage, internetCheckURL, branch, clientName, clientId , protocolUrl } = config
const maskURLs = (logString:string)=>{
  urlMasks.forEach(({key, mask}) => {
    const escapedKey = key.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
    logString = logString.replace(new RegExp(escapedKey, 'gi'), mask);
  });
  return logString;
}

if(!isDev){
  log.transports.file.fileName = `${branch.toLowerCase()}.log`;
  log.transports.file.maxSize = 1048576 * 10;
  log.transports.file.format  = ({ data, level, message }) => {
    const text:string = maskURLs(util.format(...data)).replace(/\r?\n/g, '\\n');
    const options = { month: 'short', day: 'numeric' , year:'numeric' }; // Options for formatting 
    const date =  message.date.toLocaleDateString('en-US', options);
    return `${date}|${message.date.toISOString().slice(11, -1)}|${message.variables.processType}|${level}|${text}|`;

  }
  log.initialize({ spyRendererConsole: true });

  // Save original console methods
  // const originalConsoleMethods = { ...console };
  Object.assign(console, log.functions);
  log.eventLogger.events = {
    app: {
      'certificate-error': true,
      'child-process-gone': true,
      'render-process-gone': true,
    },
    webContents: {
      'did-finish-load':true,
      'did-fail-load': true,
      'did-fail-provisional-load': true,
      'plugin-crashed': true,
      'preload-error': true,
      'unresponsive': true,
    }
  }
  log.eventLogger.startLogging()
};

let badgeCount = 0;
let zoom = 1;
let zoomInit = 1;
let maxZoomFactor = 1;
// let setVibrancy;
// if (process.platform === 'win32') {
//   setVibrancy = require("electron-acrylic-window").setVibrancy;
// }


declare const MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY: string;

let lastActivityEvent = "";
let mainConfig = {};
const store = new Store();
const storeKeysToClear = ['isOnboardDisable'];   //Add the keys that you want to clear after (CTRL/CMD)+Shift+Backspace
// const landingDomain = webpage+"/v2";
const landingDomain = webpage;
export const channelWindow = {
  setChannelWindows: 'setChannelWindows',
  close: 'windowClose',
  closeNewUpdateWindow: 'new-update-widnow-close',
  doUpdate: 'doUpdate',
  minimize: 'windowMinimize',
  sticky: 'windowSticky',
  updateHeight: 'windowHeight',
  electronVersion: 'electronVersion',
  ready: "updateWindowReady",
  windowStore:'windowStore',
  systemVersion: 'systemVersion',
  zoom: 'zoom',
  pusher: 'pusherNotifier',
  logout: 'logout',
  showNotification: 'showNotification',
  markAsReadNotification: 'markAsReadNotification',
  resetAppCacheAndStorage: 'resetAppCacheAndStorage',
  reloadWindow: 'reload-window',
  badgeCountHandler: 'badge-count',
  isMarkAsReadNotification: 'marked-as-read-notification',
  forceLogout: 'force-logout',
  handleURI: 'handle-uri',
  refreshApp: 'refresh-app',
  openNotificationSetting:'openNotificationSetting',
  productReferenceChanged: 'product-ref-data-changed',
  getAccessToken: 'get-refreshed-token',
  customNotification: 'custom-notification',
  refreshPrivateChannel: 'refresh-private-pusher',
  generatePdfResult: 'generate-pdf-result',
  fetchPdf: 'fetch-pdf',
  fetchPdfResult: 'fetch-pdf-result',
  hardReload: 'hard-reload',
  discountPriceChanged: 'discount-price-changed',
  suspendApp: 'suspend',
  resumeApp: 'resume',
  getDeviceId: 'get-device-id',
  saveForegroundBackgroundActivity: 'saveForegroundBackgroundActivity',
  resizeWindow: 'resize-window',
  setMinWindowSize: 'set-min-window-size',
  setLoginCredential: 'set-login-credential',
  getLoginCredential: 'get-login-credential',
  fetchOnRendererMount: 'fetch-on-renderer-mount',
  handleLogout: 'handle-logout',
  showLogData: 'show-log-data',
  setCofigFromRenderer: 'set-config-from-renderer',
  logData: 'log-data',
  uploadLogs:'upload-logs',
  getSignedUrl:'get-signed-url',
  ignoreMouseEvents: 'ignore-mouse-events',
  adjustWindowView: 'adjust-window-view',
  resizeScreen: 'resize-screen',
  referenceProductUpdated: 'reference-product-updated',
  windowFullScreen: 'window-full-screen',
  handleOverlay: 'handle-overlay',
  toggleFullScreen: 'toggle-full-screen',
  moveWindow: 'move-window',
  updateAvailable: 'update-available',
  rendererVersion: 'renderer-version',
  checkRendererVersion: 'check-renderer-version',
  dataChannel: 'data-channel',
  initialRedirectUri: 'initial-redirect-uri',
  getElectronConfig: 'get-electron-config',
}

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) app.quit();

// Temporary fix to ensure that we don't face RequestHeaderSectionTooLarge  problem.
// This typically occurs when the user does a force reload.
app.commandLine.appendSwitch('js-flags', '--max-old-space-size=8192');
// Function to validate blob and store in SQLite


// Check data version before app is ready (earliest possible point)
// (async function() {
//   try {
//     console.log('Checking data version at app startup...');
//     await 
//   } catch (error) {
//     console.error('Error checking data version at startup:', error);
//   }
// })();


app.whenReady().then(() => {
  // Event send when desktop resumes after sleep
  powerMonitor.on("resume",()=>{ 
    checkRendererVersion(channelWindow, browserWindow);
    browserWindow.webContents.send(channelWindow.resumeApp);
  });
});

let tray;
let iconPath;
export let browserWindow: BrowserWindow;
export let newUpdateWindow;
let originalWindowBounds = null;
let isVideoFullscreen = false;
let deepLinkUrl = null;

if (process.defaultApp) {
  if (process.argv.length >= 2) {
    app.setAsDefaultProtocolClient(protocolUrl, process.execPath, [path.resolve(process.argv[1])]);
  }
} else {
  app.setAsDefaultProtocolClient(protocolUrl)
}

const webPreferences = {
  ...(isDev && { webSecurity: false }),
  backgroundThrottling: false,
  nodeIntegration: false, // is default value after Electron v5
  contextIsolation: true, // protect against prototype pollution
  enableRemoteModule: true,
  preload: MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY,
  devTools: true
};

let browserWindowUrl = landingDomain;

const displayNoInternetScreen = () =>{
  browserWindow.loadFile(path.join(__dirname, 'public/asset', 'no-internet.html'));
}

const resetFullscreenState = () => {
  if (isVideoFullscreen && browserWindow) {
    browserWindow.setAlwaysOnTop(false);
    
    if (originalWindowBounds) {
      browserWindow.setBounds(originalWindowBounds);
      originalWindowBounds = null;
    }
    
    isVideoFullscreen = false;
  }
};

const createWindow = () => {
   iconPath = (process.platform === 'darwin') ?
    getIconPath(dockIconNameDarkMac, dockIconNameLightMac) :
    getIconPath(trayIconNameDarkWin, trayIconNameLightWin);
   
   let browserWindowHeight;
   let isOnBoardPageDisable = store.get('isOnboardDisable');
   if(!isOnBoardPageDisable){
    browserWindowHeight = 800;
    browserWindowUrl+=routes.onboardingWelcome;
   }else {
    browserWindowHeight = 108;
   }
 
  createTray();

  function isVibrancySupported() {
    // Windows 10 or greater
    return (
      process.platform === 'win32' &&
      parseInt(os.release().split('.')[0]) >= 10
    )
  }

  // let winVibrancy  = {
  //   theme: '#12345678',
  //   effect: 'acrylic',
  //   useCustomWindowRefreshMethod: false,
  //   disableOnBlur: true,
  //   debug: false,
  // }

  // const screenWidth = screen.getPrimaryDisplay().workAreaSize;
  const screenBounds = screen.getPrimaryDisplay().bounds;
  browserWindow = new BrowserWindow({
    icon: iconPath,
    // width: screenWidth.width,
    // height: screenWidth.height,
    frame: false,
    transparent: true,
    resizable: true,
    titleBarStyle:'hidden',
    roundedCorners: true, 
    titleBarOverlay: false,
    // vibrancy: 'fullscreen-ui',
    x: 0,
    y:0,
    hasShadow:false,
    minWidth: 800,
    minHeight: 600,
    webPreferences
  });
  //if(os.platform() !== 'darwin') browserWindow.setWindowButtonVisibility(false);
  
  const openAppInFullScreen = () => {
    // browserWindow.maximize();
    browserWindow.setAlwaysOnTop(false);
    const workArea = screen.getPrimaryDisplay().workAreaSize;
    let height = workArea.height;
    if(process.platform !== 'darwin') height = workArea.height - 1;
    browserWindow.setBounds({
      x: 0,
      y: 0,
      width: workArea.width,
      height
    });
  }
  browserWindow.setTitle(clientName?.toUpperCase());
  openAppInFullScreen();
  pusherInit(browserWindow, channelWindow, store);

  screen.on('display-metrics-changed', (event, display, changedMetrics) => {
    if (changedMetrics.includes('workArea') || changedMetrics.includes('bounds')) {
      if(os.platform() === 'win32') openAppInFullScreen();
    }
  });

  // if (setVibrancy) {
  // setVibrancy(browserWindow, winVibrancy);
  // }

  //nativeTheme.themeSource = 'dark';

  electronLocalshortcut.register(browserWindow, process.platform === 'darwin' ? 'Cmd+Shift+Backspace' : 'Ctrl+Shift+Backspace', clearData);
  electronLocalshortcut.register(browserWindow, process.platform === 'darwin' ? 'Cmd+R' : 'Ctrl+R', checkInternetAccessAndLoadPage);
  electronLocalshortcut.register(browserWindow, process.platform === 'darwin' ? 'Cmd+Shift+L' : 'Ctrl+Shift+L', createLogWindow);

  browserWindow.webContents.on('before-input-event', (event, input) => {
    // Check if the F11 key was pressed
    if (input.key === 'F11') {
      // Prevent the default behavior (i.e., entering full-screen mode)
      event.preventDefault();
    }
    else if (input.control && input.shift && input.code === 'Equal') { //prevent default app zoom-in for ctrl+shift+"="
      event.preventDefault();
    }
    else if(((process.platform === 'win32' && input.control) || (process.platform === 'darwin' && input.meta)) && (input.key === '-' || input.key === '=' || input.key === '0' || input.key === '+')){
    //   let isResetZoom = false;
    //   if(input.key === '0')
    //   isResetZoom = true;
    //   appZoom(input, event, isResetZoom);
      event.preventDefault();
    }
  });


  checkInternetAccessAndLoadPage();

  browserWindow.once('ready-to-show', () => {
    if(true) browserWindow.webContents.openDevTools({mode:'detach'});
    updater()
    if(process.argv.length > 0 || deepLinkUrl){
      const lastArg = deepLinkUrl || process.argv[process.argv.length - 1];
      if(lastArg && lastArg.includes(`${protocolUrl}://`)){
        browserWindow.webContents.send(channelWindow.initialRedirectUri, lastArg);
      }
    }
    // browserWindow.setIgnoreMouseEvents(true, { forward: true });
  });
 
  browserWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  if (!tray) {
    createTray();
  }

  browserWindow.on("close", ev => {
    browserWindow.hide();
    ev.preventDefault();
  });

  let isDragging = false; // Track if the window is being dragged

  browserWindow.on('resize', (event) => {
    if (isDragging) {
      event.preventDefault(); // Prevent resizing when dragging
    }
  });

  browserWindow.on('will-resize', () => {
    isDragging = true; // Set isDragging to true when dragging starts
  });
  let clearMoveTimer;
  let currentDisplayId = screen.getPrimaryDisplay().id;
  browserWindow.on('move', () => {
    const bounds = browserWindow.getBounds(); // Get window position
    const point = { x: bounds.x, y: bounds.y }; // Top-left corner
    const currentDisplay = screen.getDisplayNearestPoint(point);
    if(currentDisplayId !== currentDisplay.id){
      currentDisplayId = currentDisplay.id;
      if(clearMoveTimer){
        clearTimeout(clearMoveTimer);
      }
      clearMoveTimer = setTimeout(()=>{
      browserWindow?.setBounds({
        x: currentDisplay.bounds.x,
        y: currentDisplay.bounds.y,
        width: currentDisplay.workArea.width,
          height: currentDisplay.workArea.height
        });
        browserWindow?.webContents.send(channelWindow.moveWindow, currentDisplay.workArea);
      },200)
    }
    if (!isDragging) {
      return; // Ignore move event if not dragging
    }
    browserWindow.once('mouseup', () => {
      isDragging = false; // Reset isDragging on mouseup event
    });
    browserWindow.once('mouseleave', () => {
      isDragging = false; // Reset isDragging on mouseleave event
    });
  });

  // Synchronize focus state between browser window and update window
  browserWindow.on('focus', () => {
    if (newUpdateWindow) {
      newUpdateWindow?.setAlwaysOnTop(true);
      newUpdateWindow?.focus();
      setTimeout(()=>{
        newUpdateWindow?.setAlwaysOnTop(false);
      },1000)
    }
  });

  app.on('before-quit', ev => {
    browserWindow.removeAllListeners("close");
    browserWindow = null;
  });

  browserWindow.on('enter-full-screen', () => {
    browserWindow.webContents.send(channelWindow.toggleFullScreen, true);
  });
  
  browserWindow.on('leave-full-screen', () => {
    browserWindow.webContents.send(channelWindow.toggleFullScreen, false);
  });
  // browserWindow.on('will-move', () => {
    
  //   if (setVibrancy && isVibrancySupported()) {
  //     setVibrancy(browserWindow, null);
  //     browserWindow.setBackgroundColor('rgba(0, 0, 0, 0.75)');
  //   }
  // });

  // browserWindow.on('moved', () => {
  //   if (setVibrancy) {
  //     setVibrancy(browserWindow, winVibrancy);
  //   }
  // });  
  // Store original window bounds for restoration

  const adjustWindowForVideoPlayer = (shouldFullscreen) => {
    if (shouldFullscreen) {
      // Save current window bounds before going fullscreen
      originalWindowBounds = browserWindow.getBounds();
      isVideoFullscreen = true;
      console.log('Saved original window bounds:', originalWindowBounds);
      
      // Get the display where the window is currently positioned
      const currentDisplay = screen.getDisplayNearestPoint({ 
        x: originalWindowBounds.x, 
        y: originalWindowBounds.y 
      });
      
      // Get the full screen bounds (including areas behind taskbar/dock)
      const screenBounds = currentDisplay.bounds;
      console.log('Setting window to fullscreen bounds:', screenBounds);
      
      // Set window to appear above dock/taskbar using screen-saver level
      browserWindow.setAlwaysOnTop(true, 'screen-saver');
      
      // Set window to fullscreen size and position
      browserWindow.setBounds({
        x: screenBounds.x,
        y: screenBounds.y,
        width: screenBounds.width,
        height: screenBounds.height
      });
      
    } else {
      // Restore original window bounds
      if (originalWindowBounds) {
        console.log('Restoring original window bounds:', originalWindowBounds);
        
        // Reset always on top first
        browserWindow.setAlwaysOnTop(false);
        
        // Restore original bounds
        browserWindow.setBounds(originalWindowBounds);
        originalWindowBounds = null;
      }
      
      isVideoFullscreen = false;
    }
  }


  ipcMain.on(channelWindow.windowFullScreen, (event, data) => {
    adjustWindowForVideoPlayer(data);
  });

  ipcMain.on(channelWindow.adjustWindowView, (event) => {
    // Get current window bounds
    openAppInFullScreen();
  });

  browserWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL, isMainFrame) => {
    if(browserWindow && isMainFrame){
      // Don't show error screen for user-aborted loads (navigation, refresh, etc.)
      if (errorCode === -3) {
        console.log('Load aborted by user action or navigation, not showing error screen');
        return;
      }
      
      // The "No Internet" screen is actually a generic "failed to load" screen
      displayNoInternetScreen();
    }
  });
};

const checkInternetAccessAndLoadPage = async () => {
  if(!!browserWindow){
    browserWindow.setIgnoreMouseEvents(false, { forward: true });
    resetFullscreenState();
    
    try {
      browserWindowUrl = landingDomain;
      await fetch(internetCheckURL);
      if(!setLoadingPage('check')){
        browserWindowUrl+=routes.onboardingWelcome;
      }
      browserWindow.loadURL(encodeURI(browserWindowUrl+'?v='+new Date().getTime()));
    } catch (error) {
       if(browserWindow){
        displayNoInternetScreen()
       }
    }
  }
}

nativeTheme.on('updated', () => {
  // Only update if the theme has actually changed
  const currentIsDark = nativeTheme.shouldUseDarkColors;
  if (currentIsDark !== lastThemeState) {
    lastThemeState = currentIsDark;
    if (process.platform === 'darwin') {
      tray.setImage(nativeImage.createFromPath(getIconPath(trayIconNameDarkMac, trayIconNameLightMac)));
      app.dock.setIcon(nativeImage.createFromPath(getIconPath(dockIconNameDarkMac, dockIconNameLightMac)));
    }else{
      tray.setImage(nativeImage.createFromPath(getIconPath(trayIconNameDarkWin, trayIconNameLightWin)));
      browserWindow.setIcon(getIconPath(trayIconNameDarkWin, trayIconNameLightWin));
    }
  }
});

let lastThemeState = nativeTheme.shouldUseDarkColors;

const getIconPath = (darkIconName, lightIconName) => {
  const isDarkMode = nativeTheme.shouldUseDarkColors;
  return path.join(__dirname, 'public/asset', isDarkMode ? darkIconName : lightIconName);
};


const createTray = () => {
  iconPath = (process.platform === 'darwin') ?
    getIconPath(trayIconNameDarkMac, trayIconNameLightMac) :
    getIconPath(trayIconNameDarkWin, trayIconNameLightWin);
  tray = new Tray(iconPath);
  tray.setToolTip(clientId);

  tray.setImage(nativeImage.createFromPath(iconPath));

  if(process.platform === 'darwin'){
    app.dock.setIcon(nativeImage.createFromPath(getIconPath(dockIconNameDarkMac, dockIconNameLightMac)));
  }


  const contextMenu = Menu.buildFromTemplate([
    {
      label: `Open ${clientName}`,
      click: () => {
        browserWindow.show();
        showWindowsBadge(badgeCount);
      }
    },
    {
      role: "quit"
    }
  ]);

  tray.setContextMenu(contextMenu);
}

export const createNewUpdateWindow = () => {
  browserWindow.webContents.executeJavaScript('window.screenX').then((offsetX)=>{
    browserWindow.webContents.executeJavaScript('window.screenY').then((offsetY)=>{
      newUpdateWindow = new BrowserWindow({
        width: 400 * zoom,
        height: 355 * zoom,
        frame: false,
        transparent: true,
        resizable: false,
        modal: false,
        show: false,
        parent: null, 
        webPreferences: webPreferences,
        x: browserWindow.getBounds().x + 351 + ((800 * zoom - 400 * zoom) / 2),
        y: browserWindow.getBounds().y,
        hasShadow: false,
      });
      newUpdateWindow.loadURL(landingDomain + routes.newUpdate);
      newUpdateWindow.once("ready-to-show", () => {
        newUpdateWindow?.setAlwaysOnTop(true);
        setTimeout(()=>{
          newUpdateWindow?.setAlwaysOnTop(false);
        },1000)
        browserWindow.webContents.send(channelWindow.handleOverlay, true);
        newUpdateWindow.show();
      });  
      if (isDev) newUpdateWindow.webContents.openDevTools({ mode: 'detach' });
      
      newUpdateWindow.on('closed', () => {
        // @ts-ignore
        newUpdateWindow = null;
      });
      newUpdateWindow.webContents.on('did-finish-load', () => {
        setTimeout(() => {
          newUpdateWindow?.webContents.send(channelWindow.zoom, zoom);
        }, 500);
      });
    })
  })
}


const createReplyWindow = async (convId) => {
  try {
    const offsetX = await browserWindow.webContents.executeJavaScript('window.screenX');
    const offsetY = await browserWindow.webContents.executeJavaScript('window.screenY');

    const logWindow = new BrowserWindow({
      width: 500 * zoom,
      height: 400 * zoom,
      frame: false,
      transparent: true,
      resizable: false,
      modal: false,
      show: false,
      parent: browserWindow,
      webPreferences: webPreferences,
      x: offsetX + 50 * zoom,
      y: offsetY,
      hasShadow: false,
    });

    const htmlPath = path.join(__dirname, 'public/asset/reply.html');
    logWindow.loadURL(`file://${htmlPath}`);

    logWindow.once("ready-to-show", () => {
      logWindow.show();
    });


    // Handle IPC events for the buttons
    ipcMain.once('log-window-close', () => {
      logWindow.close();
    });

    ipcMain.once('log-window-copy', (_event) => {
      clipboard.writeText(displayContent);
      logWindow.close();
    });

    // Cleanup IPC events when the window is closed
    logWindow.on('closed', () => {
      ipcMain.removeAllListeners('log-window-close');
      ipcMain.removeAllListeners('log-window-copy');
    });
  } catch (error) {
    console.error('Error creating log window:', error);
  }
};
function toggleWindow() {
  if (!browserWindow) return;
  if (browserWindow.isVisible()) browserWindow.hide();
  else {
    browserWindow.show();
  }
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => { 
  if(browserWindow){
    electronLocalshortcut.unregisterAll(browserWindow);
    browserWindow.close()
  }
  if (process.platform !== 'darwin') {
    app.dock.hide();
  }

});

const sendActivityToRenderer = (event: string) => {
  if (lastActivityEvent === event) {
    return;
  }

  if (browserWindow?.webContents) {
    browserWindow.webContents.send(channelWindow.saveForegroundBackgroundActivity, event);
  }
  lastActivityEvent = event;
}

const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (_event, argv ,) => {
    // Someone tried to run a second instance, we should focus our window.
    if (browserWindow) {
      const lastArg = argv[argv.length - 1];
      console.log("lastArg",lastArg)
      if (lastArg) {
        if(lastArg.includes('cancel')){
          return;
        }
        browserWindow.webContents.send(channelWindow.handleURI, lastArg);
      }
      browserWindow.setAlwaysOnTop(true);
      console.log('mini',browserWindow.isMinimized())
      if (browserWindow.isMinimized()) browserWindow.restore();
      console.log('isvisible',browserWindow.isVisible())
      if(!browserWindow.isVisible()){
        browserWindow.show();
      }
      if(badgeCount>0)
      showWindowsBadge(badgeCount);
      setTimeout(()=>{
        browserWindow?.setAlwaysOnTop(false);
      },1000)
    }
  });

  // Create mainrWindow, load the rest of the app, etc...
  app.whenReady().then(() => {
    // Detect and use proxy settings
    const defaultSession = session.defaultSession;
    defaultSession.resolveProxy(landingDomain, (proxy) => {
      if (proxy === 'DIRECT') {
        console.log('No proxy detected');
      } else {
        const [type, host, port] = proxy.split(':');

        // Configure the session to use the proxy
        defaultSession.setProxy({ proxyRules: proxy }, () => {
          console.log('Proxy settings updated');
        });
      }
    });

    createWindow();
    if(!isDev){
      const exeName = path.basename(process.execPath);
      app.setLoginItemSettings({
      openAtLogin: true,
      path: process.execPath,
      args: [
      '--processStart', `${exeName}`
      ]
      });
    }
    app.on('activate', () => {
      console.log('activate ++++++++++++++++++++++++++++++++++++++++')
      if (browserWindow === null) createWindow();
      else {
        browserWindow.restore();
        if (process.platform === 'win32') browserWindow.setAlwaysOnTop(true);
      }
    });

    app.on('browser-window-focus', (event, window) => {
      checkRendererVersion(channelWindow, browserWindow);
    });

    // Get the default session object
    // const defaultSession = session.defaultSession;

    // Intercept all requests
    defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
      // Add the "Origin" header to the request
      if(!details.requestHeaders['Origin']){
        details.requestHeaders['Origin'] = landingDomain;
      }

      // Call the callback with the updated headers
      callback({ cancel: false, requestHeaders: details.requestHeaders });
    });
  
  });

  app.on('open-url', (event, url) => {
    event.preventDefault();
    // toggleWindow();
    if(app.isReady()) {
      browserWindow?.webContents.send(channelWindow.handleURI, url);
    } else {
      deepLinkUrl = url;
    }
    if (browserWindow?.isMinimized()) browserWindow.restore();
    if(!browserWindow?.isVisible()){
        browserWindow?.show();
    }
  
  });
  
}
ipcMain.on(channelWindow.ignoreMouseEvents, (event, data) => {
  browserWindow.setIgnoreMouseEvents(data, { forward: true });
});

ipcMain.on(channelWindow.openNotificationSetting, (event, data) => {
  if(data){
    if (os.platform() === "win32") {
      shell.openExternal('ms-settings:notifications');
    } else if (os.platform() === "darwin") {
      shell.openExternal('x-apple.systempreferences:com.apple.preference.notifications');
    }
    store.set('isNotificationEnabled',true)
  }else{
    event.returnValue = store.get('isNotificationEnabled') ?? false;
  }
});

ipcMain.on(channelWindow.getDeviceId, (event, data) => {
  let id = machineIdSync({ original: true });
  console.log(id, "machine-id")
  event.returnValue = id;
});

ipcMain.on(channelWindow.setChannelWindows, (event, data) => {
  event.returnValue = channelWindow;
});

ipcMain.on(channelWindow.close, () => {
  if(os.platform() === 'darwin') browserWindow.setSimpleFullScreen(false);
  browserWindow.close();
})

ipcMain.on(channelWindow.closeNewUpdateWindow, () => {
  newUpdateWindow.close();
  browserWindow.webContents.send(channelWindow.handleOverlay, false);
});

ipcMain.on(channelWindow.minimize, () => {
    if(os.platform() === 'darwin') browserWindow.setSimpleFullScreen(false);
    browserWindow.minimize();
});

ipcMain.on(channelWindow.reloadWindow, (event, data) => {
  if(browserWindow)
  checkInternetAccessAndLoadPage();
});

ipcMain.on(channelWindow.setCofigFromRenderer, (event, data) => {
  mainConfig = data;
});

ipcMain.on(channelWindow.badgeCountHandler, (event, data) => {
  if(data.type === 'reset'){
    badgeCount=0;
    showWindowsBadge(0)
  }
  else if(data.type === 'set'){
    badgeCount = data.count;
    showWindowsBadge(badgeCount);
  }
  else{
    badgeCount++;
    showWindowsBadge(badgeCount);
  }
});

// if(process.platform === 'win32'){
//   app.disableHardwareAcceleration()
// }

ipcMain.on(channelWindow.sticky, (event, data) => {
  browserWindow?.setAlwaysOnTop(data);
});

ipcMain.on(channelWindow.windowStore, (event, data) => {
  event.returnValue =  setLoadingPage(data) ;
});

ipcMain.on(channelWindow.refreshApp, (event, data) => {
  console.log("Refresh app");
  if(browserWindow)browserWindow.reload();
});
ipcMain.on(channelWindow.hardReload, (event, data) => {
  if(browserWindow)clearData();
});

const setLoadingPage = (data) => {
  let isOnBoardPageDisable = store.get('isOnboardDisable');
  isOnBoardPageDisable = store.get('isOnboardDisable');
  if(data === 'check'){
    return isOnBoardPageDisable ? true : false ;
  }else if(data === 'set'){
    store.set('isOnboardDisable',true)
  }
}

ipcMain.on(channelWindow.getElectronConfig, (event, data) => {
  event.returnValue = config;
});

ipcMain.on(channelWindow.systemVersion, (event, data) => {
  event.returnValue = getSystemVersion();
});

ipcMain.on(channelWindow.rendererVersion, (event, data) => {
  rendererVersion = data;
});

ipcMain.on(channelWindow.checkRendererVersion, (event, data) => {
  rendererVersion = data;
  checkRendererVersion(channelWindow, browserWindow);
});


ipcMain.on(channelWindow.logData, async (event, data) => {
  const logData = await getLogData(mainConfig.log.shareLogSize);
  browserWindow?.webContents.send(channelWindow.logData, logData);
});

ipcMain.on(channelWindow.platform, (event) => {
  event.returnValue = os.platform();
});

ipcMain.on(channelWindow.setLoginCredential, (event, data) => {
  store.set('autoLogin', data);
})
ipcMain.on(channelWindow.getLoginCredential, (event) => {
  event.returnValue = store.get('autoLogin');
})
ipcMain.on(channelWindow.fetchOnRendererMount, (event) => {
  const initData = {
    "autoLoginCredential": store.get('autoLogin'),
    "systemVersion": getSystemVersion(),
    "maxScreenWidth": screen.getPrimaryDisplay().workAreaSize.width,
    "maxScreenHeight": screen.getPrimaryDisplay().workAreaSize.height,
    "fullScreenHeight": screen.getPrimaryDisplay().size.height,
    "fullScreenWidth": screen.getPrimaryDisplay().size.width,
    "isFullScreen": browserWindow.isFullScreen()
  }
  event.returnValue = initData;
})
ipcMain.on(channelWindow.handleLogout, (event) => {
  store.delete('autoLogin');
})

const getSystemVersion = ()=> {
  if(os.platform() === "win32"){
    if(os.release() < '10.0.22000'){
      return 'Windows 10';
    }else{
      return 'Windows 11';
    }
  }else if(os.platform() === "darwin"){
    if(os.arch() === 'x64'){
      return 'Mac Intel';
    }else{
      return 'Mac ARM';
    } 
  }
}

ipcMain.on(channelWindow.electronVersion, (event) => {
  event.returnValue = app.getVersion();
});

ipcMain.on(channelWindow.resizeWindow, (event , shouldResize)=>{
  browserWindow.setResizable(shouldResize);
})

ipcMain.on(channelWindow.setMinWindowSize, (event, { minWidth, minHeight }) => {
  if (browserWindow) {
    browserWindow.setMinimumSize(minWidth || 800, minHeight || 600);
  }
})

ipcMain.on(channelWindow.updateHeight, (event,  {height, width}) => {
    animateResize(browserWindow, Math.round(width), Math.round(height),200);
});

ipcMain.on(channelWindow.resizeScreen, (event, data) => {
  const { width, height } = data;
  const { width: screenWidth, height: screenHeight } = screen.getDisplayMatching(browserWindow.getBounds()).workAreaSize;
  const maxScreenWidth = screenWidth * 0.7;
  const maxScreenHeight = screenHeight * 0.7;
  console.log('check width ', width, 'check height ', height, 'check maxScreenWidth ', maxScreenWidth, 'check maxScreenHeight ', maxScreenHeight, 'check screenWidth ', screenWidth, 'check screenHeight ', screenHeight);
  if(width > maxScreenWidth || height > maxScreenHeight){
    zoomInit = Math.min(screenWidth / width, screenHeight / height);
    zoomInit = Math.round(zoomInit * 10) / 10;
  }
  appZoom(null, null, true);
});

ipcMain.on(channelWindow.fetchPdf, async (event, stringPayload) => {
  const data = JSON.parse(stringPayload);
  const pdfName = data.file_name;

  let fileName = '';
  let pdfResult = {
    code: '',
    message: '',
    data: null
  };
  try {
    const url = data.url;
    const headersObj = data.headersObj;

    delete data.url;
    delete data.headersObj;

    const bufferArrayPdfData = await PdfFetcher.fetchPdf(url, headersObj, data);

    const result = await dialog.showSaveDialog(browserWindow, {
      title: 'Save',
      defaultPath: pdfName,
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] }
      ]
    })
    if (!result.canceled) {
      fileName = path.basename(result.filePath);
      const fileExt = path.extname(fileName).slice(1).toLowerCase();
      const selectedPath = path.dirname(result.filePath);
      if (fileExt === "pdf") {
        fileName = path.basename(fileName, path.extname(fileName));
      }
 
      await PdfFetcher.downloadPdf(bufferArrayPdfData, selectedPath, fileName);
      pdfResult.code = pdfMakeData.codes.success // on success
      pdfResult.message = pdfMakeData.codes.success
      pdfResult.data = { 'fileName': fileName }
    } else {
      pdfResult.code = pdfMakeData.codes.cancel // on cancel
      pdfResult.message = pdfMakeData.codes.cancel
    }
  }
  catch (error) {
    if (error.code === pdfMakeData.codes.ebusy) {
      pdfResult.code = error.code; // on file is used another software
      pdfResult.message = error.message;
      pdfResult.data = { 'fileName': fileName }
    } else {
      pdfResult.code = error.code; // on general error
      pdfResult.message = error.message;
      pdfResult.data = { 'error': error }
    }
  }
  finally {
    browserWindow?.webContents.send(channelWindow.fetchPdfResult, pdfResult);
  }
});
// let prevHeightOfFrame = 0;
let intervalId = null
function animateResize(win, width, height, duration = 200) {
  // if(true){
  console.log(width, height, "width, height")
  if(!isVideoFullscreen)
    win.setContentSize(Math.round(width*zoom),Math.round(height*zoom));
  // }
  // else {
  //   if (intervalId !== null) {
  //     clearInterval(intervalId);
  //   }
  //   const startSize = win.getSize();
  //   const targetSize = [width, height];
  //   const sizeDiff = [
  //     targetSize[0] - startSize[0],
  //     targetSize[1] - startSize[1],
  //   ];
  //   const interval = 10;
  //   const steps = duration / interval;
  //   let stepCount = 0;
  //   intervalId = setInterval(() => {
  //     stepCount++;
  //     const progress = stepCount / steps;
  //     const newSize = [
  //       startSize[0] + sizeDiff[0] * progress,
  //       startSize[1] + sizeDiff[1] * progress,
  //     ];
  //     win.setContentSize(Math.round(width*zoom),Math.round(newSize[1]*zoom));
  //     browserWindow?.webContents.send(channelWindow.zoom , zoom);
  //     if (stepCount >= steps) {
  //       if (intervalId !== null){
  //         clearInterval(intervalId);
  //         intervalId = null;
  //       }
  //     }
  //   }, interval);
  // }
  // prevHeightOfFrame = height;
}

const showWindowsBadge = (count)=>{
  if(os.platform() === "darwin")
    app.setBadgeCount(count);
  else
    app.setBadgeCount(count);
}

const clearData = async () => {
  // Clear cookies
  const cookies = session.defaultSession.cookies;
  
  await cookies.flushStore();
  session.defaultSession.clearStorageData({
    storages: ['cookies']
  }, () => {
    console.log('All cookies cleared');
  });
  for(const key of storeKeysToClear){
    store.delete(key);
  }
  console.log("Trying to clear data");
  // Clear local storage and session storage
  browserWindow.webContents.executeJavaScript(`
    localStorage.clear();
    sessionStorage.clear();
  `);
  const args = process.argv.slice(1); // Skip the first element (executable path)
  const lastArg = args[args.length - 1];
  
  // Remove the last argument if it's a clientId:// URL
  if (lastArg && lastArg.includes(`${protocolUrl}://`)) {
    args.pop();
  }
  
  app.relaunch({ args });
  app.quit();
}

function reverseFormatError(str:string) {
  return str
    .split('\n') // Split the string into lines
    .map(line => [...line].filter((_, index) => index % 2 === 0).join('')) // Process each line
    .join('\n'); // Join the processed lines back together
}

const createLogWindow = async () => {
  try {
    const displayContent = await getLogData(mainConfig.log.shareLogSize);

    const offsetX = await browserWindow.webContents.executeJavaScript('window.screenX');
    const offsetY = await browserWindow.webContents.executeJavaScript('window.screenY');

    const logWindow = new BrowserWindow({
      width: 500 * zoom,
      height: 400 * zoom,
      frame: false,
      transparent: true,
      resizable: false,
      modal: true,
      show: false,
      parent: browserWindow,
      webPreferences: webPreferences,
      x: offsetX + 50 * zoom,
      y: offsetY,
      hasShadow: false,
    });

    let content = displayContent;
    content = content.slice(-1600);

    const htmlPath = path.join(__dirname, 'public/asset/log-window.html');
    logWindow.loadURL(`file://${htmlPath}?content=${encodeURIComponent(content)}`);

    logWindow.once("ready-to-show", () => {
      logWindow.show();
    });

    if (isDev) logWindow.webContents.openDevTools({ mode: 'detach' });

    // Handle IPC events for the buttons
    ipcMain.once('log-window-close', () => {
      logWindow.close();
    });

    ipcMain.once('log-window-copy', (_event) => {
      clipboard.writeText(displayContent);
      logWindow.close();
    });

    // Cleanup IPC events when the window is closed
    logWindow.on('closed', () => {
      ipcMain.removeAllListeners('log-window-close');
      ipcMain.removeAllListeners('log-window-copy');
    });
  } catch (error) {
    console.error('Error creating log window:', error);
  }
};


const appZoom = (input, event, isResetZoom)=>{
  const { width, height } = browserWindow.getBounds();
  let newZoom = zoomInit;
  const stepSize = (maxZoomFactor - 0.5) / 5;
  if(!isResetZoom && input) newZoom = input.key === '-' ? (zoom - stepSize) :  (zoom + stepSize);
  const newWidth = Math.round(width/zoom * newZoom);
  const newHeight = Math.round(height/zoom * newZoom);
  if(newZoom >= 0.5 && newZoom <= maxZoomFactor){
    zoom = +newZoom;
    // browserWindow?.setContentSize(newWidth,newHeight);
    browserWindow?.webContents.send(channelWindow.zoom, zoom);
  }
  event?.preventDefault();
}

const getLogData = async (size: number) => {
  try {
    const logFilePath = log.transports.file.getFile().path;
    const logContent = await fs.promises.readFile(logFilePath, 'utf8'); // Read the entire log content
    if (size > 0) {
      const sizeInBytes = size * 1024 * 1024 * 2;
      return reverseFormatError(logContent.slice(-sizeInBytes));
    } else {
      return reverseFormatError(logContent);
    }
  } catch (error) {
    console.error('Error reading log file:', error);
    return 'Error reading log file.';
  }
};

 
import PdfFetcher from './PdfFetcher';
// import { createDatabaseAndTables } from './encrypted-db-utility-usage/create-database-and-tables';
// import { encryptAndPopulateDatabase } from './encrypted-db-utility-usage/encrypt-and-populate-database';
// import { calculatePrice } from './encrypted-db-utility-usage/calculate-price';
// import { getAllProducts } from './encrypted-db-utility-usage/getAllProducts';

// Helper function to round a number to specified decimal places
function round(num: number, decimalPlaces: number): number {
  const factor = Math.pow(10, decimalPlaces);
  return Math.round(num * factor) / factor;
}
